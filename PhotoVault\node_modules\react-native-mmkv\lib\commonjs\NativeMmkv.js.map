{"version": 3, "names": ["_reactNative", "require", "_ModuleNotFoundError", "_NativeMmkvPlatformContext", "Mode", "exports", "mmkvModule", "getMMKVTurboModule", "TurboModuleRegistry", "getEnforcing", "platformContext", "getMMKVPlatformContextTurboModule", "basePath", "getBaseDirectory", "initialize", "cause", "ModuleNotFoundError"], "sourceRoot": "../../src", "sources": ["NativeMmkv.ts"], "mappings": ";;;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AAEA,IAAAC,oBAAA,GAAAD,OAAA;AACA,IAAAE,0BAAA,GAAAF,OAAA;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA,IAGYG,IAAI,GAAAC,OAAA,CAAAD,IAAA,0BAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAA,OAAJA,IAAI;AAAA;AAWhB;AACA;AACA;AAqEA,IAAIE,UAAuB;AAEpB,SAASC,kBAAkBA,CAAA,EAAS;EACzC,IAAI;IACF,IAAID,UAAU,IAAI,IAAI,EAAE;MACtB;MACAA,UAAU,GAAGE,gCAAmB,CAACC,YAAY,CAAO,SAAS,CAAC;;MAE9D;MACA,MAAMC,eAAe,GAAG,IAAAC,4DAAiC,EAAC,CAAC;;MAE3D;MACA,MAAMC,QAAQ,GAAGF,eAAe,CAACG,gBAAgB,CAAC,CAAC;MACnDP,UAAU,CAACQ,UAAU,CAACF,QAAQ,CAAC;IACjC;IAEA,OAAON,UAAU;EACnB,CAAC,CAAC,OAAOS,KAAK,EAAE;IACd;IACA,MAAM,IAAIC,wCAAmB,CAACD,KAAK,CAAC;EACtC;AACF", "ignoreList": []}