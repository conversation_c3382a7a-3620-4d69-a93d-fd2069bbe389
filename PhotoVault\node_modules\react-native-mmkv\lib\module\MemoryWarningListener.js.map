{"version": 3, "names": ["AppState", "addMemoryWarningListener", "mmkv", "global", "WeakRef", "FinalizationRegistry", "weakMmkv", "listener", "addEventListener", "deref", "trim", "finalization", "l", "remove", "register"], "sourceRoot": "../../src", "sources": ["MemoryWarningListener.ts"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,cAAc;AAIvC,OAAO,SAASC,wBAAwBA,CAACC,IAAmB,EAAQ;EAClE,IAAIC,MAAM,CAACC,OAAO,IAAI,IAAI,IAAID,MAAM,CAACE,oBAAoB,IAAI,IAAI,EAAE;IACjE;IACA,MAAMC,QAAQ,GAAG,IAAIF,OAAO,CAACF,IAAI,CAAC;IAClC,MAAMK,QAAQ,GAAGP,QAAQ,CAACQ,gBAAgB,CAAC,eAAe,EAAE,MAAM;MAChE;MACAF,QAAQ,CAACG,KAAK,CAAC,CAAC,EAAEC,IAAI,CAAC,CAAC;IAC1B,CAAC,CAAC;IACF;IACA,MAAMC,YAAY,GAAG,IAAIN,oBAAoB,CAC1CO,CAA0B,IAAK;MAC9B;MACAA,CAAC,CAACC,MAAM,CAAC,CAAC;IACZ,CACF,CAAC;IACD;IACAF,YAAY,CAACG,QAAQ,CAACZ,IAAI,EAAEK,QAAQ,CAAC;EACvC,CAAC,MAAM;IACL;IACA;IACAP,QAAQ,CAACQ,gBAAgB,CAAC,eAAe,EAAE,MAAM;MAC/CN,IAAI,CAACQ,IAAI,CAAC,CAAC;IACb,CAAC,CAAC;EACJ;AACF", "ignoreList": []}