{"version": 3, "names": ["createMockMMKV", "storage", "Map", "clearAll", "clear", "delete", "key", "set", "value", "getString", "result", "get", "undefined", "getNumber", "getBoolean", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "getAllKeys", "Array", "from", "keys", "contains", "has", "recrypt", "console", "warn", "size", "isReadOnly", "trim"], "sourceRoot": "../../src", "sources": ["createMMKV.mock.ts"], "mappings": ";;AAEA;AACA,OAAO,MAAMA,cAAc,GAAGA,CAAA,KAAkB;EAC9C,MAAMC,OAAO,GAAG,IAAIC,GAAG,CAAkD,CAAC;EAE1E,OAAO;IACLC,QAAQ,EAAEA,CAAA,KAAMF,OAAO,CAACG,KAAK,CAAC,CAAC;IAC/BC,MAAM,EAAGC,GAAG,IAAKL,OAAO,CAACI,MAAM,CAACC,GAAG,CAAC;IACpCC,GAAG,EAAEA,CAACD,GAAG,EAAEE,KAAK,KAAKP,OAAO,CAACM,GAAG,CAACD,GAAG,EAAEE,KAAK,CAAC;IAC5CC,SAAS,EAAGH,GAAG,IAAK;MAClB,MAAMI,MAAM,GAAGT,OAAO,CAACU,GAAG,CAACL,GAAG,CAAC;MAC/B,OAAO,OAAOI,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGE,SAAS;IACxD,CAAC;IACDC,SAAS,EAAGP,GAAG,IAAK;MAClB,MAAMI,MAAM,GAAGT,OAAO,CAACU,GAAG,CAACL,GAAG,CAAC;MAC/B,OAAO,OAAOI,MAAM,KAAK,QAAQ,GAAGA,MAAM,GAAGE,SAAS;IACxD,CAAC;IACDE,UAAU,EAAGR,GAAG,IAAK;MACnB,MAAMI,MAAM,GAAGT,OAAO,CAACU,GAAG,CAACL,GAAG,CAAC;MAC/B,OAAO,OAAOI,MAAM,KAAK,SAAS,GAAGA,MAAM,GAAGE,SAAS;IACzD,CAAC;IACDG,SAAS,EAAGT,GAAG,IAAK;MAClB,MAAMI,MAAM,GAAGT,OAAO,CAACU,GAAG,CAACL,GAAG,CAAC;MAC/B,OAAOI,MAAM,YAAYM,WAAW,GAAGN,MAAM,GAAGE,SAAS;IAC3D,CAAC;IACDK,UAAU,EAAEA,CAAA,KAAMC,KAAK,CAACC,IAAI,CAAClB,OAAO,CAACmB,IAAI,CAAC,CAAC,CAAC;IAC5CC,QAAQ,EAAGf,GAAG,IAAKL,OAAO,CAACqB,GAAG,CAAChB,GAAG,CAAC;IACnCiB,OAAO,EAAEA,CAAA,KAAM;MACbC,OAAO,CAACC,IAAI,CAAC,uDAAuD,CAAC;IACvE,CAAC;IACDC,IAAI,EAAE,CAAC;IACPC,UAAU,EAAE,KAAK;IACjBC,IAAI,EAAEA,CAAA,KAAM;MACV;IAAA;EAEJ,CAAC;AACH,CAAC", "ignoreList": []}