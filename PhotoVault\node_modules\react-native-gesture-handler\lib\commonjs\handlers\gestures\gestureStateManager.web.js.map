{"version": 3, "names": ["_NodeManager", "_interopRequireDefault", "require", "e", "__esModule", "default", "GestureStateManager", "exports", "create", "handlerTag", "begin", "NodeManager", "<PERSON><PERSON><PERSON><PERSON>", "activate", "fail", "end"], "sourceRoot": "../../../../src", "sources": ["handlers/gestures/gestureStateManager.web.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,sBAAA,CAAAC,OAAA;AAAsD,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAG/C,MAAMG,mBAAmB,GAAAC,OAAA,CAAAD,mBAAA,GAAG;EACjCE,MAAMA,CAACC,UAAkB,EAA2B;IAClD,OAAO;MACLC,KAAK,EAAEA,CAAA,KAAM;QACXC,oBAAW,CAACC,UAAU,CAACH,UAAU,CAAC,CAACC,KAAK,CAAC,CAAC;MAC5C,CAAC;MAEDG,QAAQ,EAAEA,CAAA,KAAM;QACdF,oBAAW,CAACC,UAAU,CAACH,UAAU,CAAC,CAACI,QAAQ,CAAC,IAAI,CAAC;MACnD,CAAC;MAEDC,IAAI,EAAEA,CAAA,KAAM;QACVH,oBAAW,CAACC,UAAU,CAACH,UAAU,CAAC,CAACK,IAAI,CAAC,CAAC;MAC3C,CAAC;MAEDC,GAAG,EAAEA,CAAA,KAAM;QACTJ,oBAAW,CAACC,UAAU,CAACH,UAAU,CAAC,CAACM,GAAG,CAAC,CAAC;MAC1C;IACF,CAAC;EACH;AACF,CAAC", "ignoreList": []}