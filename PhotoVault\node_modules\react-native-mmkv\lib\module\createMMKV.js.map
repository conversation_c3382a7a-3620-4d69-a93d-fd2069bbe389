{"version": 3, "names": ["Platform", "getMMKVTurboModule", "Mode", "getMMKVPlatformContextTurboModule", "createMMKV", "config", "module", "OS", "path", "appGroupDirectory", "getAppGroupDirectory", "e", "console", "error", "mode", "instance", "__DEV__", "Error"], "sourceRoot": "../../src", "sources": ["createMMKV.ts"], "mappings": ";;AAAA,SAASA,QAAQ,QAAQ,cAAc;AACvC,SAASC,kBAAkB,QAAQ,cAAc;AACjD,SAA6BC,IAAI,QAAyB,SAAS;AACnE,SAASC,iCAAiC,QAAQ,6BAA6B;AAE/E,OAAO,MAAMC,UAAU,GAAIC,MAAqB,IAAiB;EAC/D,MAAMC,MAAM,GAAGL,kBAAkB,CAAC,CAAC;EAEnC,IAAID,QAAQ,CAACO,EAAE,KAAK,KAAK,EAAE;IACzB,IAAIF,MAAM,CAACG,IAAI,IAAI,IAAI,EAAE;MACvB,IAAI;QACF;QACA,MAAMC,iBAAiB,GACrBN,iCAAiC,CAAC,CAAC,CAACO,oBAAoB,CAAC,CAAC;QAC5D,IAAID,iBAAiB,IAAI,IAAI,EAAE;UAC7B;UACAJ,MAAM,CAACG,IAAI,GAAGC,iBAAiB;QACjC;MACF,CAAC,CAAC,OAAOE,CAAC,EAAE;QACV;QACAC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;MAClB;IACF;EACF;EAEA,IAAI,OAAON,MAAM,CAACS,IAAI,KAAK,QAAQ,EAAE;IACnC;IACA;IACA;IACAT,MAAM,CAACS,IAAI,GAAGZ,IAAI,CAACG,MAAM,CAACS,IAAI,CAAC;EACjC;EAEA,MAAMC,QAAQ,GAAGT,MAAM,CAACF,UAAU,CAACC,MAAM,CAAC;EAC1C,IAAIW,OAAO,EAAE;IACX,IAAI,OAAOD,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,IAAI,IAAI,EAAE;MACpD,MAAM,IAAIE,KAAK,CACb,oFACF,CAAC;IACH;EACF;EACA,OAAOF,QAAQ;AACjB,CAAC", "ignoreList": []}