{"version": 3, "names": ["_hammerjs", "_interopRequireDefault", "require", "_constants", "_DraggingGestureHandler", "_utils", "_State", "e", "__esModule", "default", "PanGestureHandler", "DraggingGestureHandler", "name", "NativeGestureClass", "Hammer", "Pan", "getHammerConfig", "direction", "getDirection", "getState", "type", "nextState", "previousState", "State", "UNDETERMINED", "ACTIVE", "BEGAN", "config", "getConfig", "activeOffsetXStart", "activeOffsetXEnd", "activeOffsetYStart", "activeOffsetYEnd", "minDist", "directions", "horizontalDirections", "isnan", "DIRECTION_ALL", "push", "DIRECTION_LEFT", "DIRECTION_RIGHT", "length", "DIRECTION_HORIZONTAL", "concat", "verticalDirections", "DIRECTION_UP", "DIRECTION_DOWN", "DIRECTION_VERTICAL", "DIRECTION_NONE", "hasCustomActivationCriteria", "minDistSq", "shouldFailUnderCustomCriteria", "deltaX", "deltaY", "criteria", "failOffsetXStart", "failOffsetXEnd", "failOffsetYStart", "failOffsetYEnd", "shouldActivateUnderCustomCriteria", "velocity", "TEST_MIN_IF_NOT_NAN", "VEC_LEN_SQ", "x", "y", "minVelocityX", "minVelocityY", "minVelocitySq", "shouldMultiFingerPanFail", "pointer<PERSON><PERSON><PERSON>", "scale", "deltaRotation", "deltaScale", "Math", "abs", "absDeltaRotation", "MULTI_FINGER_PAN_MAX_PINCH_THRESHOLD", "MULTI_FINGER_PAN_MAX_ROTATION_THRESHOLD", "updateHasCustomActivationCriteria", "isValidNumber", "isGestureEnabledForEvent", "props", "_recognizer", "inputData", "failed", "velocityX", "velocityY", "maxPointers", "success", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["web_hammer/PanGestureHandler.ts"], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,UAAA,GAAAD,OAAA;AAKA,IAAAE,uBAAA,GAAAH,sBAAA,CAAAC,OAAA;AACA,IAAAG,MAAA,GAAAH,OAAA;AACA,IAAAI,MAAA,GAAAJ,OAAA;AAAiC,SAAAD,uBAAAM,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAGjC,MAAMG,iBAAiB,SAASC,+BAAsB,CAAC;EACrD,IAAIC,IAAIA,CAAA,EAAG;IACT,OAAO,KAAK;EACd;EAEA,IAAIC,kBAAkBA,CAAA,EAAG;IACvB,OAAOC,iBAAM,CAACC,GAAG;EACnB;EAEAC,eAAeA,CAAA,EAAG;IAChB,OAAO;MACL,GAAG,KAAK,CAACA,eAAe,CAAC,CAAC;MAC1BC,SAAS,EAAE,IAAI,CAACC,YAAY,CAAC;IAC/B,CAAC;EACH;EAEAC,QAAQA,CAACC,IAA2B,EAAE;IACpC,MAAMC,SAAS,GAAG,KAAK,CAACF,QAAQ,CAACC,IAAI,CAAC;IACtC;IACA,IACE,IAAI,CAACE,aAAa,KAAKC,YAAK,CAACC,YAAY,IACzCH,SAAS,KAAKE,YAAK,CAACE,MAAM,EAC1B;MACA,OAAOF,YAAK,CAACG,KAAK;IACpB;IACA,OAAOL,SAAS;EAClB;EAEAH,YAAYA,CAAA,EAAG;IACb,MAAMS,MAAM,GAAG,IAAI,CAACC,SAAS,CAAC,CAAC;IAC/B,MAAM;MACJC,kBAAkB;MAClBC,gBAAgB;MAChBC,kBAAkB;MAClBC,gBAAgB;MAChBC;IACF,CAAC,GAAGN,MAAM;IACV,IAAIO,UAAoB,GAAG,EAAE;IAC7B,IAAIC,oBAAoB,GAAG,EAAE;IAE7B,IAAI,CAAC,IAAAC,YAAK,EAACH,OAAO,CAAC,EAAE;MACnB,OAAOnB,iBAAM,CAACuB,aAAa;IAC7B;IAEA,IAAI,CAAC,IAAAD,YAAK,EAACP,kBAAkB,CAAC,EAAE;MAC9BM,oBAAoB,CAACG,IAAI,CAACxB,iBAAM,CAACyB,cAAc,CAAC;IAClD;IACA,IAAI,CAAC,IAAAH,YAAK,EAACN,gBAAgB,CAAC,EAAE;MAC5BK,oBAAoB,CAACG,IAAI,CAACxB,iBAAM,CAAC0B,eAAe,CAAC;IACnD;IACA,IAAIL,oBAAoB,CAACM,MAAM,KAAK,CAAC,EAAE;MACrCN,oBAAoB,GAAG,CAACrB,iBAAM,CAAC4B,oBAAoB,CAAC;IACtD;IAEAR,UAAU,GAAGA,UAAU,CAACS,MAAM,CAACR,oBAAoB,CAAC;IACpD,IAAIS,kBAAkB,GAAG,EAAE;IAE3B,IAAI,CAAC,IAAAR,YAAK,EAACL,kBAAkB,CAAC,EAAE;MAC9Ba,kBAAkB,CAACN,IAAI,CAACxB,iBAAM,CAAC+B,YAAY,CAAC;IAC9C;IACA,IAAI,CAAC,IAAAT,YAAK,EAACJ,gBAAgB,CAAC,EAAE;MAC5BY,kBAAkB,CAACN,IAAI,CAACxB,iBAAM,CAACgC,cAAc,CAAC;IAChD;IAEA,IAAIF,kBAAkB,CAACH,MAAM,KAAK,CAAC,EAAE;MACnCG,kBAAkB,GAAG,CAAC9B,iBAAM,CAACiC,kBAAkB,CAAC;IAClD;IAEAb,UAAU,GAAGA,UAAU,CAACS,MAAM,CAACC,kBAAkB,CAAC;IAElD,IAAI,CAACV,UAAU,CAACO,MAAM,EAAE;MACtB,OAAO3B,iBAAM,CAACkC,cAAc;IAC9B;IACA,IACEd,UAAU,CAAC,CAAC,CAAC,KAAKpB,iBAAM,CAAC4B,oBAAoB,IAC7CR,UAAU,CAAC,CAAC,CAAC,KAAKpB,iBAAM,CAACiC,kBAAkB,EAC3C;MACA,OAAOjC,iBAAM,CAACuB,aAAa;IAC7B;IACA,IAAIF,oBAAoB,CAACM,MAAM,IAAIG,kBAAkB,CAACH,MAAM,EAAE;MAC5D,OAAO3B,iBAAM,CAACuB,aAAa;IAC7B;IAEA,OAAOH,UAAU,CAAC,CAAC,CAAC;EACtB;EAEAN,SAASA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAACqB,2BAA2B,EAAE;MACrC;MACA;MACA,OAAO;QACLC,SAAS,EAAE;MACb,CAAC;IACH;IACA,OAAO,IAAI,CAACvB,MAAM;EACpB;EAEAwB,6BAA6BA,CAC3B;IAAEC,MAAM;IAAEC;EAAuB,CAAC,EAClCC,QAAa,EACb;IACA,OACG,CAAC,IAAAlB,YAAK,EAACkB,QAAQ,CAACC,gBAAgB,CAAC,IAChCH,MAAM,GAAGE,QAAQ,CAACC,gBAAgB,IACnC,CAAC,IAAAnB,YAAK,EAACkB,QAAQ,CAACE,cAAc,CAAC,IAAIJ,MAAM,GAAGE,QAAQ,CAACE,cAAe,IACpE,CAAC,IAAApB,YAAK,EAACkB,QAAQ,CAACG,gBAAgB,CAAC,IAChCJ,MAAM,GAAGC,QAAQ,CAACG,gBAAiB,IACpC,CAAC,IAAArB,YAAK,EAACkB,QAAQ,CAACI,cAAc,CAAC,IAAIL,MAAM,GAAGC,QAAQ,CAACI,cAAe;EAEzE;EAEAC,iCAAiCA,CAC/B;IAAEP,MAAM;IAAEC,MAAM;IAAEO;EAAc,CAAC,EACjCN,QAAa,EACb;IACA,OACG,CAAC,IAAAlB,YAAK,EAACkB,QAAQ,CAACzB,kBAAkB,CAAC,IAClCuB,MAAM,GAAGE,QAAQ,CAACzB,kBAAkB,IACrC,CAAC,IAAAO,YAAK,EAACkB,QAAQ,CAACxB,gBAAgB,CAAC,IAChCsB,MAAM,GAAGE,QAAQ,CAACxB,gBAAiB,IACpC,CAAC,IAAAM,YAAK,EAACkB,QAAQ,CAACvB,kBAAkB,CAAC,IAClCsB,MAAM,GAAGC,QAAQ,CAACvB,kBAAmB,IACtC,CAAC,IAAAK,YAAK,EAACkB,QAAQ,CAACtB,gBAAgB,CAAC,IAChCqB,MAAM,GAAGC,QAAQ,CAACtB,gBAAiB,IACrC,IAAA6B,0BAAmB,EACjB,IAAAC,iBAAU,EAAC;MAAEC,CAAC,EAAEX,MAAM;MAAEY,CAAC,EAAEX;IAAO,CAAC,CAAC,EACpCC,QAAQ,CAACJ,SACX,CAAC,IACD,IAAAW,0BAAmB,EAACD,QAAQ,CAACG,CAAC,EAAET,QAAQ,CAACW,YAAY,CAAC,IACtD,IAAAJ,0BAAmB,EAACD,QAAQ,CAACI,CAAC,EAAEV,QAAQ,CAACY,YAAY,CAAC,IACtD,IAAAL,0BAAmB,EAAC,IAAAC,iBAAU,EAACF,QAAQ,CAAC,EAAEN,QAAQ,CAACa,aAAa,CAAC;EAErE;EAEAC,wBAAwBA,CAAC;IACvBC,aAAa;IACbC,KAAK;IACLC;EAKF,CAAC,EAAE;IACD,IAAIF,aAAa,IAAI,CAAC,EAAE;MACtB,OAAO,KAAK;IACd;;IAEA;IACA,MAAMG,UAAU,GAAGC,IAAI,CAACC,GAAG,CAACJ,KAAK,GAAG,CAAC,CAAC;IACtC,MAAMK,gBAAgB,GAAGF,IAAI,CAACC,GAAG,CAACH,aAAa,CAAC;IAChD,IAAIC,UAAU,GAAGI,+CAAoC,EAAE;MACrD;MACA;MACA,OAAO,IAAI;IACb;IACA,IAAID,gBAAgB,GAAGE,kDAAuC,EAAE;MAC9D;MACA;MACA,OAAO,IAAI;IACb;IAEA,OAAO,KAAK;EACd;EAEAC,iCAAiCA,CAC/BxB,QAAmE,EACnE;IACA,OACE,IAAAyB,oBAAa,EAACzB,QAAQ,CAACJ,SAAS,CAAC,IACjC,IAAA6B,oBAAa,EAACzB,QAAQ,CAACW,YAAY,CAAC,IACpC,IAAAc,oBAAa,EAACzB,QAAQ,CAACY,YAAY,CAAC,IACpC,IAAAa,oBAAa,EAACzB,QAAQ,CAACa,aAAa,CAAC,IACrC,IAAAY,oBAAa,EAACzB,QAAQ,CAACzB,kBAAkB,CAAC,IAC1C,IAAAkD,oBAAa,EAACzB,QAAQ,CAACxB,gBAAgB,CAAC,IACxC,IAAAiD,oBAAa,EAACzB,QAAQ,CAACvB,kBAAkB,CAAC,IAC1C,IAAAgD,oBAAa,EAACzB,QAAQ,CAACtB,gBAAgB,CAAC;EAE5C;EAEAgD,wBAAwBA,CACtBC,KAAU,EACVC,WAAgB,EAChBC,SAAqD,EACrD;IACA,IAAI,IAAI,CAAChC,6BAA6B,CAACgC,SAAS,EAAEF,KAAK,CAAC,EAAE;MACxD,OAAO;QAAEG,MAAM,EAAE;MAAK,CAAC;IACzB;IAEA,MAAMxB,QAAQ,GAAG;MAAEG,CAAC,EAAEoB,SAAS,CAACE,SAAS;MAAErB,CAAC,EAAEmB,SAAS,CAACG;IAAU,CAAC;IACnE,IACE,IAAI,CAACrC,2BAA2B,IAChC,IAAI,CAACU,iCAAiC,CACpC;MAAEP,MAAM,EAAE+B,SAAS,CAAC/B,MAAM;MAAEC,MAAM,EAAE8B,SAAS,CAAC9B,MAAM;MAAEO;IAAS,CAAC,EAChEqB,KACF,CAAC,EACD;MACA,IACE,IAAI,CAACb,wBAAwB,CAAC;QAC5BC,aAAa,EAAEc,SAAS,CAACI,WAAW;QACpCjB,KAAK,EAAEa,SAAS,CAACb,KAAK;QACtBC,aAAa,EAAEY,SAAS,CAACZ;MAC3B,CAAC,CAAC,EACF;QACA,OAAO;UACLa,MAAM,EAAE;QACV,CAAC;MACH;MACA,OAAO;QAAEI,OAAO,EAAE;MAAK,CAAC;IAC1B;IACA,OAAO;MAAEA,OAAO,EAAE;IAAM,CAAC;EAC3B;AACF;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAjF,OAAA,GAEcC,iBAAiB", "ignoreList": []}