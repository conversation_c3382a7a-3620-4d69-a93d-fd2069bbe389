{"version": 3, "names": ["TurboModuleRegistry", "ModuleNotFoundError", "mmkvPlatformModule", "getMMKVPlatformContextTurboModule", "getEnforcing", "e"], "sourceRoot": "../../src", "sources": ["NativeMmkvPlatformContext.ts"], "mappings": ";;AACA,SAASA,mBAAmB,QAAQ,cAAc;AAClD,SAASC,mBAAmB,QAAQ,uBAAuB;AAoB3D,IAAIC,kBAA+B;AAEnC,OAAO,SAASC,iCAAiCA,CAAA,EAAS;EACxD,IAAI;IACF,IAAID,kBAAkB,IAAI,IAAI,EAAE;MAC9B;MACAA,kBAAkB,GAAGF,mBAAmB,CAACI,YAAY,CACnD,qBACF,CAAC;IACH;IACA,OAAOF,kBAAkB;EAC3B,CAAC,CAAC,OAAOG,CAAC,EAAE;IACV;IACA,MAAM,IAAIJ,mBAAmB,CAACI,CAAC,CAAC;EAClC;AACF", "ignoreList": []}