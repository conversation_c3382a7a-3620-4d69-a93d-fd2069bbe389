# 🚀 LocalOne Vault - Development Build Guide

This guide explains how to create a development build that enables **full secure deletion** functionality.

## 🔐 Why Development Build?

**Expo Go Limitations:**
- ❌ Cannot delete photos from device gallery
- ❌ Limited native module access
- ✅ Good for testing UI and basic functionality

**Development Build Benefits:**
- ✅ **Full secure deletion** - automatically removes photos from gallery
- ✅ Complete native module access
- ✅ Production-like functionality
- ✅ Better performance

## 📋 Prerequisites

1. **Expo Account**: Create a free account at [expo.dev](https://expo.dev)
2. **EAS CLI**: Already installed in this project
3. **Android Studio** (for Android builds) or **Xcode** (for iOS builds)

## 🛠️ Build Process

### Step 1: Login to Expo
```bash
npx eas login
```

### Step 2: Configure Build
```bash
npx eas build:configure
```

### Step 3: Build for Android (APK)
```bash
npx eas build --platform android --profile development
```

### Step 4: Build for iOS (Development)
```bash
npx eas build --platform ios --profile development
```

## 📱 Installation Options

### Option A: Direct Download
1. After build completes, you'll get a download link
2. Download the APK (Android) or install via TestFlight (iOS)
3. Install on your device

### Option B: QR Code Install
1. Scan the QR code provided after build
2. Follow installation instructions

## 🔐 Secure Deletion Features

Once you have the development build installed:

### ✅ **Automatic Secure Import:**
- Photos are **automatically deleted** from your gallery after import
- No manual deletion required
- True "move" operation instead of "copy"

### ✅ **Enhanced Security Messages:**
- Success confirmation when original is deleted
- Clear feedback about security status
- No more manual deletion reminders

### ✅ **Production-Ready:**
- All features work as intended
- Better performance than Expo Go
- Ready for app store distribution

## 🚀 Quick Start Commands

```bash
# 1. Login to Expo
npx eas login

# 2. Build Android development version
npx eas build --platform android --profile development

# 3. Build iOS development version  
npx eas build --platform ios --profile development

# 4. Build production APK
npx eas build --platform android --profile production
```

## 📊 Build Profiles

The `eas.json` file contains three build profiles:

### 🔧 **Development**
- For testing with full native features
- Includes development tools
- Enables secure deletion

### 👀 **Preview** 
- For sharing with testers
- Optimized but not production-ready
- Good for demos

### 🚀 **Production**
- App store ready
- Fully optimized
- All security features enabled

## 🔒 Security Benefits

With development build, LocalOne Vault provides:

1. **True Secure Import**: Photos are moved, not copied
2. **Automatic Cleanup**: No manual deletion needed
3. **Zero Traces**: Photos completely removed from gallery
4. **Maximum Privacy**: Only exists in your secure vault

## 📞 Support

If you encounter issues:
1. Check [Expo Documentation](https://docs.expo.dev/build/introduction/)
2. Verify all prerequisites are installed
3. Ensure you're logged into Expo CLI

## 🎯 Next Steps

1. **Create Expo account** if you don't have one
2. **Run the build commands** above
3. **Install the development build** on your device
4. **Test secure import** - photos will be automatically deleted from gallery!

---

**Note**: The first build may take 10-20 minutes. Subsequent builds are faster due to caching.
