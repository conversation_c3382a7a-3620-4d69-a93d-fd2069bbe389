import { MMKV } from 'react-native-mmkv';
import * as FileSystem from 'expo-file-system';
import { ImageItem, STORAGE_KEYS } from '../types';

class StorageService {
  private storage: MMKV;
  private vaultDirectory: string;

  constructor() {
    this.storage = new MMKV();
    this.vaultDirectory = `${FileSystem.documentDirectory}PhotoVault/`;
    this.initializeVaultDirectory();
  }

  private async initializeVaultDirectory() {
    try {
      const dirInfo = await FileSystem.getInfoAsync(this.vaultDirectory);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(this.vaultDirectory, { intermediates: true });
      }
    } catch (error) {
      console.error('Error initializing vault directory:', error);
    }
  }

  // PIN Management
  setPIN(pin: string): void {
    this.storage.set(STORAGE_KEYS.PIN, pin);
  }

  getPIN(): string | undefined {
    return this.storage.getString(STORAGE_KEYS.PIN);
  }

  verifyPIN(inputPin: string): boolean {
    const storedPin = this.getPIN();
    return storedPin === inputPin;
  }

  // First Launch Management
  setFirstLaunch(isFirst: boolean): void {
    this.storage.set(STORAGE_KEYS.IS_FIRST_LAUNCH, isFirst);
  }

  isFirstLaunch(): boolean {
    return this.storage.getBoolean(STORAGE_KEYS.IS_FIRST_LAUNCH) ?? true;
  }

  // Image Management
  getImages(): ImageItem[] {
    try {
      const imagesJson = this.storage.getString(STORAGE_KEYS.IMAGES);
      return imagesJson ? JSON.parse(imagesJson) : [];
    } catch (error) {
      console.error('Error getting images:', error);
      return [];
    }
  }

  saveImages(images: ImageItem[]): void {
    try {
      this.storage.set(STORAGE_KEYS.IMAGES, JSON.stringify(images));
    } catch (error) {
      console.error('Error saving images:', error);
    }
  }

  async addImage(sourceUri: string, filename?: string): Promise<ImageItem | null> {
    try {
      const id = Date.now().toString();
      const extension = sourceUri.split('.').pop() || 'jpg';
      const finalFilename = filename || `image_${id}.${extension}`;
      const destinationUri = `${this.vaultDirectory}${finalFilename}`;

      // Copy image to vault directory
      await FileSystem.copyAsync({
        from: sourceUri,
        to: destinationUri,
      });

      // Get file info
      const fileInfo = await FileSystem.getInfoAsync(destinationUri);

      const newImage: ImageItem = {
        id,
        uri: destinationUri,
        filename: finalFilename,
        createdAt: Date.now(),
        size: fileInfo.size,
      };

      // Update images list
      const currentImages = this.getImages();
      const updatedImages = [newImage, ...currentImages];
      this.saveImages(updatedImages);

      return newImage;
    } catch (error) {
      console.error('Error adding image:', error);
      return null;
    }
  }

  async deleteImage(imageId: string): Promise<boolean> {
    try {
      const images = this.getImages();
      const imageToDelete = images.find(img => img.id === imageId);
      
      if (!imageToDelete) {
        return false;
      }

      // Delete file from filesystem
      await FileSystem.deleteAsync(imageToDelete.uri, { idempotent: true });

      // Remove from images list
      const updatedImages = images.filter(img => img.id !== imageId);
      this.saveImages(updatedImages);

      return true;
    } catch (error) {
      console.error('Error deleting image:', error);
      return false;
    }
  }

  async clearAllData(): Promise<void> {
    try {
      // Clear MMKV storage
      this.storage.clearAll();
      
      // Delete all files in vault directory
      const dirInfo = await FileSystem.getInfoAsync(this.vaultDirectory);
      if (dirInfo.exists) {
        await FileSystem.deleteAsync(this.vaultDirectory, { idempotent: true });
        await this.initializeVaultDirectory();
      }
    } catch (error) {
      console.error('Error clearing all data:', error);
    }
  }
}

export default new StorageService();
