import AsyncStorage from "@react-native-async-storage/async-storage";
import * as FileSystem from "expo-file-system";
import { ImageItem, STORAGE_KEYS } from "../types";

class StorageService {
  private vaultDirectory: string;

  constructor() {
    this.vaultDirectory = `${FileSystem.documentDirectory}LocalOneVault/`;
    this.initializeVaultDirectory();
  }

  private async initializeVaultDirectory() {
    try {
      const dirInfo = await FileSystem.getInfoAsync(this.vaultDirectory);
      if (!dirInfo.exists) {
        await FileSystem.makeDirectoryAsync(this.vaultDirectory, {
          intermediates: true,
        });
      }
    } catch (error) {
      console.error("Error initializing vault directory:", error);
    }
  }

  // PIN Management
  async setPIN(pin: string): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.PIN, pin);
    } catch (error) {
      console.error("Error setting PIN:", error);
    }
  }

  async getPIN(): Promise<string | null> {
    try {
      return await AsyncStorage.getItem(STORAGE_KEYS.PIN);
    } catch (error) {
      console.error("Error getting PIN:", error);
      return null;
    }
  }

  async verifyPIN(inputPin: string): Promise<boolean> {
    const storedPin = await this.getPIN();
    return storedPin === inputPin;
  }

  // First Launch Management
  async setFirstLaunch(isFirst: boolean): Promise<void> {
    try {
      await AsyncStorage.setItem(
        STORAGE_KEYS.IS_FIRST_LAUNCH,
        isFirst.toString()
      );
    } catch (error) {
      console.error("Error setting first launch:", error);
    }
  }

  async isFirstLaunch(): Promise<boolean> {
    try {
      const value = await AsyncStorage.getItem(STORAGE_KEYS.IS_FIRST_LAUNCH);
      return value === null || value === "true";
    } catch (error) {
      console.error("Error getting first launch:", error);
      return true;
    }
  }

  // Image Management
  async getImages(): Promise<ImageItem[]> {
    try {
      const imagesJson = await AsyncStorage.getItem(STORAGE_KEYS.IMAGES);
      return imagesJson ? JSON.parse(imagesJson) : [];
    } catch (error) {
      console.error("Error getting images:", error);
      return [];
    }
  }

  async saveImages(images: ImageItem[]): Promise<void> {
    try {
      await AsyncStorage.setItem(STORAGE_KEYS.IMAGES, JSON.stringify(images));
    } catch (error) {
      console.error("Error saving images:", error);
    }
  }

  async addImage(
    sourceUri: string,
    filename?: string
  ): Promise<ImageItem | null> {
    try {
      const id = Date.now().toString();
      const extension = sourceUri.split(".").pop() || "jpg";
      const finalFilename = filename || `image_${id}.${extension}`;
      const destinationUri = `${this.vaultDirectory}${finalFilename}`;

      // Copy image to vault directory
      await FileSystem.copyAsync({
        from: sourceUri,
        to: destinationUri,
      });

      // Get file info
      const fileInfo = await FileSystem.getInfoAsync(destinationUri);

      const newImage: ImageItem = {
        id,
        uri: destinationUri,
        filename: finalFilename,
        createdAt: Date.now(),
        size: fileInfo.exists ? (fileInfo as any).size : undefined,
      };

      // Update images list
      const currentImages = await this.getImages();
      const updatedImages = [newImage, ...currentImages];
      await this.saveImages(updatedImages);

      return newImage;
    } catch (error) {
      console.error("Error adding image:", error);
      return null;
    }
  }

  async deleteImage(imageId: string): Promise<boolean> {
    try {
      const images = await this.getImages();
      const imageToDelete = images.find((img) => img.id === imageId);

      if (!imageToDelete) {
        return false;
      }

      // Delete file from filesystem
      await FileSystem.deleteAsync(imageToDelete.uri, { idempotent: true });

      // Remove from images list
      const updatedImages = images.filter((img) => img.id !== imageId);
      await this.saveImages(updatedImages);

      return true;
    } catch (error) {
      console.error("Error deleting image:", error);
      return false;
    }
  }

  async clearAllData(): Promise<void> {
    try {
      // Clear AsyncStorage
      await AsyncStorage.clear();

      // Delete all files in vault directory
      const dirInfo = await FileSystem.getInfoAsync(this.vaultDirectory);
      if (dirInfo.exists) {
        await FileSystem.deleteAsync(this.vaultDirectory, { idempotent: true });
        await this.initializeVaultDirectory();
      }
    } catch (error) {
      console.error("Error clearing all data:", error);
    }
  }
}

export default new StorageService();
