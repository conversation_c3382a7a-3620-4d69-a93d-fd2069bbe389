{"version": 3, "names": ["_react", "_interopRequireDefault", "require", "_reactNative", "e", "__esModule", "default", "_extends", "Object", "assign", "bind", "n", "arguments", "length", "t", "r", "hasOwnProperty", "call", "apply", "BottomNavigationRouteScreen", "React", "Component", "render", "style", "index", "children", "visibility", "rest", "props", "display", "Platform", "OS", "undefined", "createElement", "View", "testID", "_default", "exports", "Animated", "createAnimatedComponent"], "sourceRoot": "../../../../src", "sources": ["components/BottomNavigation/BottomNavigationRouteScreen.tsx"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAAmE,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAG,SAAA,WAAAA,QAAA,GAAAC,MAAA,CAAAC,MAAA,GAAAD,MAAA,CAAAC,MAAA,CAAAC,IAAA,eAAAC,CAAA,aAAAP,CAAA,MAAAA,CAAA,GAAAQ,SAAA,CAAAC,MAAA,EAAAT,CAAA,UAAAU,CAAA,GAAAF,SAAA,CAAAR,CAAA,YAAAW,CAAA,IAAAD,CAAA,OAAAE,cAAA,CAAAC,IAAA,CAAAH,CAAA,EAAAC,CAAA,MAAAJ,CAAA,CAAAI,CAAA,IAAAD,CAAA,CAAAC,CAAA,aAAAJ,CAAA,KAAAJ,QAAA,CAAAW,KAAA,OAAAN,SAAA;AAOnE,MAAMO,2BAA2B,SAASC,cAAK,CAACC,SAAS,CAAQ;EAC/DC,MAAMA,CAAA,EAAgB;IACpB,MAAM;MAAEC,KAAK;MAAEC,KAAK;MAAEC,QAAQ;MAAEC,UAAU;MAAE,GAAGC;IAAK,CAAC,GAAG,IAAI,CAACC,KAAK;;IAElE;IACA;IACA;IACA,MAAMC,OAAO,GACXC,qBAAQ,CAACC,EAAE,KAAK,KAAK,GAAIL,UAAU,KAAK,CAAC,GAAG,MAAM,GAAG,MAAM,GAAIM,SAAS;IAE1E,oBACEhC,MAAA,CAAAM,OAAA,CAAA2B,aAAA,CAAC9B,YAAA,CAAA+B,IAAI,EAAA3B,QAAA;MACH4B,MAAM,EAAE,gBAAgBX,KAAK,EAAG;MAChCD,KAAK,EAAE,CAACA,KAAK,EAAE;QAAEM;MAAQ,CAAC;IAAE,GACxBF,IAAI,GAEPF,QACG,CAAC;EAEX;AACF;AAAC,IAAAW,QAAA,GAAAC,OAAA,CAAA/B,OAAA,GAEcgC,qBAAQ,CAACC,uBAAuB,CAACpB,2BAA2B,CAAC", "ignoreList": []}