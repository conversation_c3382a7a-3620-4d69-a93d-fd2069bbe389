import React, { useState, useRef } from 'react';
import { View, StyleSheet, Alert } from 'react-native';
import { CameraView, CameraType, useCameraPermissions } from 'expo-camera';
import { Button, Appbar, ActivityIndicator, Text } from 'react-native-paper';
import { StackNavigationProp } from '@react-navigation/stack';
import StorageService from '../services/StorageService';
import { RootStackParamList } from '../types';

type CameraScreenNavigationProp = StackNavigationProp<RootStackParamList, 'Camera'>;

interface Props {
  navigation: CameraScreenNavigationProp;
}

const CameraScreen: React.FC<Props> = ({ navigation }) => {
  const [facing, setFacing] = useState<CameraType>('back');
  const [permission, requestPermission] = useCameraPermissions();
  const [isCapturing, setIsCapturing] = useState(false);
  const cameraRef = useRef<CameraView>(null);

  if (!permission) {
    // Camera permissions are still loading
    return (
      <View style={styles.container}>
        <ActivityIndicator size="large" />
      </View>
    );
  }

  if (!permission.granted) {
    // Camera permissions are not granted yet
    return (
      <View style={styles.container}>
        <Appbar.Header>
          <Appbar.BackAction onPress={() => navigation.goBack()} />
          <Appbar.Content title="Camera" />
        </Appbar.Header>
        <View style={styles.permissionContainer}>
          <Text variant="headlineSmall" style={styles.permissionTitle}>
            Camera Permission Required
          </Text>
          <Text variant="bodyLarge" style={styles.permissionText}>
            PhotoVault needs camera access to take photos for your vault.
          </Text>
          <Button mode="contained" onPress={requestPermission} style={styles.permissionButton}>
            Grant Permission
          </Button>
        </View>
      </View>
    );
  }

  const toggleCameraFacing = () => {
    setFacing(current => (current === 'back' ? 'front' : 'back'));
  };

  const takePicture = async () => {
    if (!cameraRef.current || isCapturing) return;

    try {
      setIsCapturing(true);
      
      const photo = await cameraRef.current.takePictureAsync({
        quality: 0.8,
        base64: false,
        skipProcessing: false,
      });

      if (photo?.uri) {
        // Save the photo to the vault
        const savedImage = await StorageService.addImage(photo.uri);
        
        if (savedImage) {
          Alert.alert(
            'Photo Saved',
            'Your photo has been securely saved to the vault.',
            [
              {
                text: 'Take Another',
                style: 'default',
              },
              {
                text: 'View Gallery',
                style: 'default',
                onPress: () => navigation.navigate('Gallery'),
              },
            ]
          );
        } else {
          Alert.alert('Error', 'Failed to save photo. Please try again.');
        }
      }
    } catch (error) {
      console.error('Error taking picture:', error);
      Alert.alert('Error', 'Failed to take photo. Please try again.');
    } finally {
      setIsCapturing(false);
    }
  };

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.BackAction onPress={() => navigation.goBack()} />
        <Appbar.Content title="Take Photo" />
        <Appbar.Action icon="camera-flip" onPress={toggleCameraFacing} />
      </Appbar.Header>

      <CameraView 
        style={styles.camera} 
        facing={facing}
        ref={cameraRef}
      >
        <View style={styles.cameraOverlay}>
          <View style={styles.captureContainer}>
            <Button
              mode="contained"
              onPress={takePicture}
              disabled={isCapturing}
              loading={isCapturing}
              style={styles.captureButton}
              labelStyle={styles.captureButtonText}
            >
              {isCapturing ? 'Saving...' : 'Capture'}
            </Button>
          </View>
        </View>
      </CameraView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  camera: {
    flex: 1,
  },
  cameraOverlay: {
    flex: 1,
    backgroundColor: 'transparent',
    justifyContent: 'flex-end',
  },
  captureContainer: {
    alignItems: 'center',
    paddingBottom: 50,
  },
  captureButton: {
    width: 80,
    height: 80,
    borderRadius: 40,
    justifyContent: 'center',
    alignItems: 'center',
  },
  captureButtonText: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  permissionContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  permissionTitle: {
    textAlign: 'center',
    marginBottom: 20,
    fontWeight: 'bold',
  },
  permissionText: {
    textAlign: 'center',
    marginBottom: 30,
    color: '#666',
  },
  permissionButton: {
    paddingHorizontal: 20,
  },
});

export default CameraScreen;
