import React, { useState, useEffect, useCallback } from "react";
import {
  View,
  StyleSheet,
  FlatList,
  Alert,
  RefreshControl,
} from "react-native";
import { Appbar, FAB, Text, Portal, Dialog, Button } from "react-native-paper";
import { StackNavigationProp } from "@react-navigation/stack";
import { useFocusEffect } from "@react-navigation/native";
import GalleryItem from "../components/GalleryItem";
import StorageService from "../services/StorageService";
import MediaService from "../services/MediaService";
import { RootStackParamList, ImageItem } from "../types";

type GalleryScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  "Gallery"
>;

interface Props {
  navigation: GalleryScreenNavigationProp;
}

const GalleryScreen: React.FC<Props> = ({ navigation }) => {
  const [images, setImages] = useState<ImageItem[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [selectedImage, setSelectedImage] = useState<ImageItem | null>(null);
  const [showDeleteDialog, setShowDeleteDialog] = useState(false);
  const [fabOpen, setFabOpen] = useState(false);

  const loadImages = useCallback(async () => {
    const vaultImages = await StorageService.getImages();
    setImages(vaultImages);
  }, []);

  useFocusEffect(
    useCallback(() => {
      loadImages();
    }, [loadImages])
  );

  const handleRefresh = async () => {
    setIsLoading(true);
    await loadImages();
    setIsLoading(false);
  };

  const handleImagePress = (item: ImageItem) => {
    // In a real app, you might want to show a full-screen image viewer
    Alert.alert(
      item.filename,
      `Created: ${new Date(item.createdAt).toLocaleString()}\nSize: ${
        item.size ? Math.round(item.size / 1024) + " KB" : "Unknown"
      }`,
      [{ text: "OK" }]
    );
  };

  const handleImageLongPress = (item: ImageItem) => {
    setSelectedImage(item);
    setShowDeleteDialog(true);
  };

  const handleDeleteImage = async () => {
    if (!selectedImage) return;

    try {
      const success = await StorageService.deleteImage(selectedImage.id);
      if (success) {
        await loadImages(); // Refresh the gallery
        Alert.alert("Success", "Image deleted successfully.");
      } else {
        Alert.alert("Error", "Failed to delete image.");
      }
    } catch (error) {
      Alert.alert("Error", "Failed to delete image.");
    } finally {
      setShowDeleteDialog(false);
      setSelectedImage(null);
    }
  };

  const handleImportFromGallery = async () => {
    setFabOpen(false);
    const success = await MediaService.selectAndImportImage();
    if (success) {
      await loadImages(); // Refresh the gallery
    }
  };

  const handleTakePhoto = () => {
    setFabOpen(false);
    navigation.navigate("Camera");
  };

  const renderEmptyState = () => (
    <View style={styles.emptyContainer}>
      <Text variant="headlineSmall" style={styles.emptyTitle}>
        📸 Your Vault is Empty
      </Text>
      <Text variant="bodyLarge" style={styles.emptyText}>
        Start building your secure photo collection by taking a photo or
        importing from your gallery.
      </Text>
    </View>
  );

  const renderImage = ({ item }: { item: ImageItem }) => (
    <GalleryItem
      item={item}
      onPress={handleImagePress}
      onLongPress={handleImageLongPress}
    />
  );

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.Content title="PhotoVault" />
        <Appbar.Action icon="refresh" onPress={handleRefresh} />
      </Appbar.Header>

      {images.length === 0 ? (
        renderEmptyState()
      ) : (
        <FlatList
          data={images}
          renderItem={renderImage}
          keyExtractor={(item) => item.id}
          numColumns={3}
          contentContainerStyle={styles.gallery}
          refreshControl={
            <RefreshControl refreshing={isLoading} onRefresh={handleRefresh} />
          }
        />
      )}

      <FAB.Group
        open={fabOpen}
        visible
        icon={fabOpen ? "close" : "plus"}
        actions={[
          {
            icon: "camera",
            label: "Take Photo",
            onPress: handleTakePhoto,
          },
          {
            icon: "image",
            label: "Import from Gallery",
            onPress: handleImportFromGallery,
          },
        ]}
        onStateChange={({ open }) => setFabOpen(open)}
      />

      <Portal>
        <Dialog
          visible={showDeleteDialog}
          onDismiss={() => setShowDeleteDialog(false)}
        >
          <Dialog.Title>Delete Image</Dialog.Title>
          <Dialog.Content>
            <Text variant="bodyMedium">
              Are you sure you want to delete "{selectedImage?.filename}"? This
              action cannot be undone.
            </Text>
          </Dialog.Content>
          <Dialog.Actions>
            <Button onPress={() => setShowDeleteDialog(false)}>Cancel</Button>
            <Button onPress={handleDeleteImage} textColor="#d32f2f">
              Delete
            </Button>
          </Dialog.Actions>
        </Dialog>
      </Portal>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  gallery: {
    padding: 10,
  },
  emptyContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    padding: 40,
  },
  emptyTitle: {
    textAlign: "center",
    marginBottom: 20,
    fontWeight: "bold",
  },
  emptyText: {
    textAlign: "center",
    color: "#666",
    lineHeight: 24,
  },
});

export default GalleryScreen;
