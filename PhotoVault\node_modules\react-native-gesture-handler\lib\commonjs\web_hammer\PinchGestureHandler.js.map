{"version": 3, "names": ["_hammerjs", "_interopRequireDefault", "require", "_IndiscreteGestureHandler", "e", "__esModule", "default", "PinchGestureHandler", "IndiscreteGestureHandler", "name", "NativeGestureClass", "Hammer", "Pinch", "transformNativeEvent", "scale", "velocity", "center", "focalX", "x", "focalY", "y", "_default", "exports"], "sourceRoot": "../../../src", "sources": ["web_hammer/PinchGestureHandler.ts"], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAGA,IAAAC,yBAAA,GAAAF,sBAAA,CAAAC,OAAA;AAAkE,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAElE,MAAMG,mBAAmB,SAASC,iCAAwB,CAAC;EACzD,IAAIC,IAAIA,CAAA,EAAG;IACT,OAAO,OAAO;EAChB;EAEA,IAAIC,kBAAkBA,CAAA,EAAG;IACvB,OAAOC,iBAAM,CAACC,KAAK;EACrB;EAEAC,oBAAoBA,CAAC;IAAEC,KAAK;IAAEC,QAAQ;IAAEC;EAAuB,CAAC,EAAE;IAChE,OAAO;MACLC,MAAM,EAAED,MAAM,CAACE,CAAC;MAChBC,MAAM,EAAEH,MAAM,CAACI,CAAC;MAChBL,QAAQ;MACRD;IACF,CAAC;EACH;AACF;AAAC,IAAAO,QAAA,GAAAC,OAAA,CAAAhB,OAAA,GAEcC,mBAAmB", "ignoreList": []}