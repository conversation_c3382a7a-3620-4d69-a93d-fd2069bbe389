{"version": 3, "names": ["_reactNative", "require", "BULLET_POINT", "messageWithSuggestions", "message", "suggestions", "join", "getFrameworkType", "ExpoConstants", "NativeModules", "NativeUnimoduleProxy", "modulesConstants", "ExponentConstants", "appOwnership", "ModuleNotFoundError", "Error", "constructor", "cause", "global", "__turboModuleProxy", "push", "error", "framework", "Platform", "OS", "exports"], "sourceRoot": "../../src", "sources": ["ModuleNotFoundError.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAOA,MAAMC,YAAY,GAAG,MAAM;AAE3B,SAASC,sBAAsBA,CAC7BC,OAAe,EACfC,WAAqB,EACb;EACR,OAAOD,OAAO,GAAGF,YAAY,GAAGG,WAAW,CAACC,IAAI,CAACJ,YAAY,CAAC;AAChE;AAEA,SAASK,gBAAgBA,CAAA,EAAwC;EAC/D;EACA,MAAMC,aAAa,GACjBC,0BAAa,CAACC,oBAAoB,EAAEC,gBAAgB,EAAEC,iBAAiB;EACzE,IAAIJ,aAAa,IAAI,IAAI,EAAE;IACzB,IAAIA,aAAa,CAACK,YAAY,KAAK,MAAM,EAAE;MACzC;MACA,OAAO,SAAS;IAClB,CAAC,MAAM;MACL;MACA,OAAO,MAAM;IACf;EACF;EACA,OAAO,cAAc;AACvB;AAEO,MAAMC,mBAAmB,SAASC,KAAK,CAAC;EAC7CC,WAAWA,CAACC,KAAe,EAAE;IAC3B;IACA,IAAIC,MAAM,CAACC,kBAAkB,IAAI,IAAI,EAAE;MACrC;MACA;MACA;MACA,MAAMf,OAAO,GACX,+HAA+H;MACjI,MAAMC,WAAqB,GAAG,EAAE;MAChCA,WAAW,CAACe,IAAI,CACd,mFACF,CAAC;MACDf,WAAW,CAACe,IAAI,CACd,sKACF,CAAC;MACD,MAAMC,KAAK,GAAGlB,sBAAsB,CAACC,OAAO,EAAEC,WAAW,CAAC;MAC1D,KAAK,CAACgB,KAAK,EAAE;QAAEJ,KAAK,EAAEA;MAAM,CAAC,CAAC;MAC9B;IACF;IAEA,MAAMK,SAAS,GAAGf,gBAAgB,CAAC,CAAC;IACpC,IAAIe,SAAS,KAAK,SAAS,EAAE;MAC3B,KAAK,CACH,+GACF,CAAC;MACD;IACF;IAEA,MAAMlB,OAAO,GACX,kFAAkF;IACpF,MAAMC,WAAqB,GAAG,EAAE;IAChCA,WAAW,CAACe,IAAI,CACd,+FACF,CAAC;IACDf,WAAW,CAACe,IAAI,CACd,kNACF,CAAC;IACDf,WAAW,CAACe,IAAI,CACd,wGACF,CAAC;IACDf,WAAW,CAACe,IAAI,CAAC,gCAAgC,CAAC;IAClD,IAAIE,SAAS,KAAK,MAAM,EAAE;MACxBjB,WAAW,CAACe,IAAI,CAAC,oCAAoC,CAAC;IACxD;IACA,QAAQG,qBAAQ,CAACC,EAAE;MACjB,KAAK,KAAK;MACV,KAAK,OAAO;QACVnB,WAAW,CAACe,IAAI,CACd,wDACF,CAAC;QACD;MACF,KAAK,SAAS;QACZf,WAAW,CAACe,IAAI,CAAC,6BAA6B,CAAC;QAC/C;MACF;QACE,MAAM,IAAIL,KAAK,CAAC,4BAA4BQ,qBAAQ,CAACC,EAAE,GAAG,CAAC;IAC/D;IAEA,MAAMH,KAAK,GAAGlB,sBAAsB,CAACC,OAAO,EAAEC,WAAW,CAAC;IAC1D,KAAK,CAACgB,KAAK,EAAE;MAAEJ,KAAK,EAAEA;IAAM,CAAC,CAAC;EAChC;AACF;AAACQ,OAAA,CAAAX,mBAAA,GAAAA,mBAAA", "ignoreList": []}