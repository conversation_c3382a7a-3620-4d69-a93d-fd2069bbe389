{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_CheckboxAndroid", "_interopRequireDefault", "_CheckboxIOS", "_theming", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "Checkbox", "theme", "themeOverrides", "props", "useInternalTheme", "Platform", "OS", "createElement", "_default", "exports", "CheckboxWithTheme"], "sourceRoot": "../../../../src", "sources": ["components/Checkbox/Checkbox.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,gBAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,YAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,QAAA,GAAAL,OAAA;AAAsD,SAAAG,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAP,wBAAAO,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAX,uBAAA,YAAAA,CAAAO,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAAA,SAAAgB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAf,CAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAK,CAAA,IAAAF,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAZ,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAa,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAkCtD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,QAAQ,GAAGA,CAAC;EAAEC,KAAK,EAAEC,cAAc;EAAE,GAAGC;AAAa,CAAC,KAAK;EAC/D,MAAMF,KAAK,GAAG,IAAAG,yBAAgB,EAACF,cAAc,CAAC;EAC9C,OAAOG,qBAAQ,CAACC,EAAE,KAAK,KAAK,gBAC1BvC,KAAA,CAAAwC,aAAA,CAAClC,YAAA,CAAAI,OAAW,EAAAiB,QAAA,KAAKS,KAAK;IAAEF,KAAK,EAAEA;EAAM,EAAE,CAAC,gBAExClC,KAAA,CAAAwC,aAAA,CAACpC,gBAAA,CAAAM,OAAe,EAAAiB,QAAA,KAAKS,KAAK;IAAEF,KAAK,EAAEA;EAAM,EAAE,CAC5C;AACH,CAAC;AAAC,IAAAO,QAAA,GAAAC,OAAA,CAAAhC,OAAA,GAEauB,QAAQ,EAEvB;AACA,MAAMU,iBAAiB,GAAAD,OAAA,CAAAT,QAAA,GAAGA,QAAQ;AAClC", "ignoreList": []}