{"version": 3, "names": ["createTextEncoder", "canUseDOM", "window", "document", "createElement", "hasAccessToLocalStorage", "localStorage", "KEY_WILDCARD", "inMemoryStorage", "Map", "createMMKV", "config", "<PERSON><PERSON><PERSON>", "Error", "path", "console", "warn", "storage", "getItem", "key", "get", "setItem", "value", "set", "removeItem", "delete", "clear", "length", "size", "index", "Object", "keys", "at", "domStorage", "global", "textEncoder", "id", "includes", "keyPrefix", "prefixedKey", "clearAll", "startsWith", "toString", "getString", "undefined", "getNumber", "Number", "getBoolean", "<PERSON><PERSON><PERSON><PERSON>", "encode", "buffer", "getAllKeys", "filter", "map", "slice", "contains", "recrypt", "isReadOnly", "trim"], "sourceRoot": "../../src", "sources": ["createMMKV.web.ts"], "mappings": ";;AAAA;;AAEA,SAASA,iBAAiB,QAAQ,qBAAqB;AAEvD,MAAMC,SAAS,GACb,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,QAAQ,EAAEC,aAAa,IAAI,IAAI;AAEzE,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;EACpC,IAAI;IACF;IACAH,MAAM,CAACI,YAAY;IAEnB,OAAO,IAAI;EACb,CAAC,CAAC,MAAM;IACN,OAAO,KAAK;EACd;AACF,CAAC;AAED,MAAMC,YAAY,GAAG,IAAI;AACzB,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAAiB,CAAC;AAEjD,OAAO,MAAMC,UAAU,GAAIC,MAAqB,IAAiB;EAC/D,IAAIA,MAAM,CAACC,aAAa,IAAI,IAAI,EAAE;IAChC,MAAM,IAAIC,KAAK,CAAC,gDAAgD,CAAC;EACnE;EACA,IAAIF,MAAM,CAACG,IAAI,IAAI,IAAI,EAAE;IACvB,MAAM,IAAID,KAAK,CAAC,uCAAuC,CAAC;EAC1D;;EAEA;EACA,IAAI,CAACR,uBAAuB,CAAC,CAAC,IAAIJ,SAAS,EAAE;IAC3Cc,OAAO,CAACC,IAAI,CACV,6FACF,CAAC;EACH;EAEA,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpB,IAAI,CAAChB,SAAS,EAAE;MACd,MAAM,IAAIY,KAAK,CACb,kFACF,CAAC;IACH;IAEA,IAAI,CAACR,uBAAuB,CAAC,CAAC,EAAE;MAC9B,OAAO;QACLa,OAAO,EAAGC,GAAW,IAAKX,eAAe,CAACY,GAAG,CAACD,GAAG,CAAC,IAAI,IAAI;QAC1DE,OAAO,EAAEA,CAACF,GAAW,EAAEG,KAAa,KAClCd,eAAe,CAACe,GAAG,CAACJ,GAAG,EAAEG,KAAK,CAAC;QACjCE,UAAU,EAAGL,GAAW,IAAKX,eAAe,CAACiB,MAAM,CAACN,GAAG,CAAC;QACxDO,KAAK,EAAEA,CAAA,KAAMlB,eAAe,CAACkB,KAAK,CAAC,CAAC;QACpCC,MAAM,EAAEnB,eAAe,CAACoB,IAAI;QAC5BT,GAAG,EAAGU,KAAa,IAAKC,MAAM,CAACC,IAAI,CAACvB,eAAe,CAAC,CAACwB,EAAE,CAACH,KAAK,CAAC,IAAI;MACpE,CAAC;IACH;IAEA,MAAMI,UAAU,GACdC,MAAM,EAAE5B,YAAY,IAAIJ,MAAM,EAAEI,YAAY,IAAIA,YAAY;IAC9D,IAAI2B,UAAU,IAAI,IAAI,EAAE;MACtB,MAAM,IAAIpB,KAAK,CAAC,yCAAyC,CAAC;IAC5D;IACA,OAAOoB,UAAU;EACnB,CAAC;EAED,MAAME,WAAW,GAAGnC,iBAAiB,CAAC,CAAC;EAEvC,IAAIW,MAAM,CAACyB,EAAE,CAACC,QAAQ,CAAC9B,YAAY,CAAC,EAAE;IACpC,MAAM,IAAIM,KAAK,CACb,2DACF,CAAC;EACH;EAEA,MAAMyB,SAAS,GAAG,GAAG3B,MAAM,CAACyB,EAAE,GAAG7B,YAAY,EAAE,CAAC,CAAC;EACjD,MAAMgC,WAAW,GAAIpB,GAAW,IAAK;IACnC,IAAIA,GAAG,CAACkB,QAAQ,CAAC,IAAI,CAAC,EAAE;MACtB,MAAM,IAAIxB,KAAK,CACb,4DACF,CAAC;IACH;IACA,OAAO,GAAGyB,SAAS,GAAGnB,GAAG,EAAE;EAC7B,CAAC;EAED,OAAO;IACLqB,QAAQ,EAAEA,CAAA,KAAM;MACd,MAAMT,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACd,OAAO,CAAC,CAAC,CAAC;MACnC,KAAK,MAAME,GAAG,IAAIY,IAAI,EAAE;QACtB,IAAIZ,GAAG,CAACsB,UAAU,CAACH,SAAS,CAAC,EAAE;UAC7BrB,OAAO,CAAC,CAAC,CAACO,UAAU,CAACL,GAAG,CAAC;QAC3B;MACF;IACF,CAAC;IACDM,MAAM,EAAGN,GAAG,IAAKF,OAAO,CAAC,CAAC,CAACO,UAAU,CAACe,WAAW,CAACpB,GAAG,CAAC,CAAC;IACvDI,GAAG,EAAEA,CAACJ,GAAG,EAAEG,KAAK,KAAK;MACnBL,OAAO,CAAC,CAAC,CAACI,OAAO,CAACkB,WAAW,CAACpB,GAAG,CAAC,EAAEG,KAAK,CAACoB,QAAQ,CAAC,CAAC,CAAC;IACvD,CAAC;IACDC,SAAS,EAAGxB,GAAG,IAAKF,OAAO,CAAC,CAAC,CAACC,OAAO,CAACqB,WAAW,CAACpB,GAAG,CAAC,CAAC,IAAIyB,SAAS;IACpEC,SAAS,EAAG1B,GAAG,IAAK;MAClB,MAAMG,KAAK,GAAGL,OAAO,CAAC,CAAC,CAACC,OAAO,CAACqB,WAAW,CAACpB,GAAG,CAAC,CAAC;MACjD,IAAIG,KAAK,IAAI,IAAI,EAAE,OAAOsB,SAAS;MACnC,OAAOE,MAAM,CAACxB,KAAK,CAAC;IACtB,CAAC;IACDyB,UAAU,EAAG5B,GAAG,IAAK;MACnB,MAAMG,KAAK,GAAGL,OAAO,CAAC,CAAC,CAACC,OAAO,CAACqB,WAAW,CAACpB,GAAG,CAAC,CAAC;MACjD,IAAIG,KAAK,IAAI,IAAI,EAAE,OAAOsB,SAAS;MACnC,OAAOtB,KAAK,KAAK,MAAM;IACzB,CAAC;IACD0B,SAAS,EAAG7B,GAAG,IAAK;MAClB,MAAMG,KAAK,GAAGL,OAAO,CAAC,CAAC,CAACC,OAAO,CAACqB,WAAW,CAACpB,GAAG,CAAC,CAAC;MACjD,IAAIG,KAAK,IAAI,IAAI,EAAE,OAAOsB,SAAS;MACnC,OAAOT,WAAW,CAACc,MAAM,CAAC3B,KAAK,CAAC,CAAC4B,MAAM;IACzC,CAAC;IACDC,UAAU,EAAEA,CAAA,KAAM;MAChB,MAAMpB,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACd,OAAO,CAAC,CAAC,CAAC;MACnC,OAAOc,IAAI,CACRqB,MAAM,CAAEjC,GAAG,IAAKA,GAAG,CAACsB,UAAU,CAACH,SAAS,CAAC,CAAC,CAC1Ce,GAAG,CAAElC,GAAG,IAAKA,GAAG,CAACmC,KAAK,CAAChB,SAAS,CAACX,MAAM,CAAC,CAAC;IAC9C,CAAC;IACD4B,QAAQ,EAAGpC,GAAG,IAAKF,OAAO,CAAC,CAAC,CAACC,OAAO,CAACqB,WAAW,CAACpB,GAAG,CAAC,CAAC,IAAI,IAAI;IAC9DqC,OAAO,EAAEA,CAAA,KAAM;MACb,MAAM,IAAI3C,KAAK,CAAC,wCAAwC,CAAC;IAC3D,CAAC;IACDe,IAAI,EAAE,CAAC;IACP6B,UAAU,EAAE,KAAK;IACjBC,IAAI,EAAEA,CAAA,KAAM;MACV;IAAA;EAEJ,CAAC;AACH,CAAC", "ignoreList": []}