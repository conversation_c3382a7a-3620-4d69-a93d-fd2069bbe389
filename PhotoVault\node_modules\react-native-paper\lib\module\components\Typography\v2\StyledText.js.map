{"version": 3, "names": ["React", "I18nManager", "StyleSheet", "color", "Text", "useInternalTheme", "StyledText", "alpha", "family", "style", "theme", "themeOverrides", "rest", "_theme$colors", "_theme$fonts", "textColor", "isV3", "colors", "onSurface", "text", "rgb", "string", "writingDirection", "getConstants", "isRTL", "createElement", "_extends", "styles", "fonts", "create", "textAlign"], "sourceRoot": "../../../../../src", "sources": ["components/Typography/v2/StyledText.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,WAAW,EAAaC,UAAU,QAAmB,cAAc;AAE5E,OAAOC,KAAK,MAAM,OAAO;AAGzB,OAAOC,IAAI,MAAM,QAAQ;AACzB,SAASC,gBAAgB,QAAQ,uBAAuB;AASxD,MAAMC,UAAU,GAAGA,CAAC;EAClBC,KAAK,GAAG,CAAC;EACTC,MAAM;EACNC,KAAK;EACLC,KAAK,EAAEC,cAAc;EACrB,GAAGC;AACE,CAAC,KAAK;EAAA,IAAAC,aAAA,EAAAC,YAAA;EACX,MAAMJ,KAAK,GAAGL,gBAAgB,CAACM,cAAc,CAAC;EAE9C,MAAMI,SAAS,GAAGZ,KAAK,CACrBO,KAAK,CAACM,IAAI,GAAGN,KAAK,CAACO,MAAM,CAACC,SAAS,IAAAL,aAAA,GAAGH,KAAK,CAACO,MAAM,cAAAJ,aAAA,uBAAZA,aAAA,CAAcM,IACtD,CAAC,CACEZ,KAAK,CAACA,KAAK,CAAC,CACZa,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;EACX,MAAMC,gBAAgB,GAAGrB,WAAW,CAACsB,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK;EAEzE,oBACExB,KAAA,CAAAyB,aAAA,CAACrB,IAAI,EAAAsB,QAAA,KACCd,IAAI;IACRH,KAAK,EAAE,CACLkB,MAAM,CAACR,IAAI,EACX;MACEhB,KAAK,EAAEY,SAAS;MAChB,IAAI,CAACL,KAAK,CAACM,IAAI,MAAAF,YAAA,GAAIJ,KAAK,CAACkB,KAAK,cAAAd,YAAA,uBAAXA,YAAA,CAAcN,MAAM,CAAC,EAAC;MACzCc;IACF,CAAC,EACDb,KAAK;EACL,EACH,CAAC;AAEN,CAAC;AAED,MAAMkB,MAAM,GAAGzB,UAAU,CAAC2B,MAAM,CAAC;EAC/BV,IAAI,EAAE;IACJW,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAEF,eAAexB,UAAU", "ignoreList": []}