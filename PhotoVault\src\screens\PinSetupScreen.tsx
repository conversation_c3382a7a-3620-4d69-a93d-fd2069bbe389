import React, { useState } from "react";
import { View, StyleSheet, Alert } from "react-native";
import { Text, Appbar } from "react-native-paper";
import { StackNavigationProp } from "@react-navigation/stack";
import PinInput from "../components/PinInput";
import StorageService from "../services/StorageService";
import { RootStackParamList } from "../types";

type PinSetupScreenNavigationProp = StackNavigationProp<
  RootStackParamList,
  "PinSetup"
>;

interface Props {
  navigation: PinSetupScreenNavigationProp;
}

const PinSetupScreen: React.FC<Props> = ({ navigation }) => {
  const [pin, setPin] = useState("");
  const [confirmPin, setConfirmPin] = useState("");
  const [step, setStep] = useState<"setup" | "confirm">("setup");
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState("");

  const handlePinSubmit = async () => {
    if (step === "setup") {
      if (pin.length >= 4 && pin.length <= 6) {
        setStep("confirm");
        setError("");
      }
    } else {
      // Confirm step
      if (confirmPin.length >= 4 && confirmPin.length <= 6) {
        if (pin === confirmPin) {
          setIsLoading(true);
          try {
            // Save PIN and mark first launch as complete
            await StorageService.setPIN(pin);
            await StorageService.setFirstLaunch(false);

            // Navigate to gallery
            navigation.replace("Gallery");
          } catch (error) {
            setError("Failed to save PIN. Please try again.");
          } finally {
            setIsLoading(false);
          }
        } else {
          setError("PINs do not match. Please try again.");
          setStep("setup");
          setPin("");
          setConfirmPin("");
        }
      }
    }
  };

  const handleBackPress = () => {
    if (step === "confirm") {
      setStep("setup");
      setConfirmPin("");
      setError("");
    } else {
      Alert.alert(
        "Exit App",
        "Are you sure you want to exit? You need to set up a PIN to use PhotoVault.",
        [
          { text: "Cancel", style: "cancel" },
          {
            text: "Exit",
            style: "destructive",
            onPress: () => {
              // In a real app, you might want to close the app or show an error
            },
          },
        ]
      );
    }
  };

  const getCurrentPin = () => {
    return step === "setup" ? pin : confirmPin;
  };

  const handlePinChange = (newPin: string) => {
    setError("");
    if (step === "setup") {
      setPin(newPin);
    } else {
      setConfirmPin(newPin);
    }
  };

  return (
    <View style={styles.container}>
      <Appbar.Header>
        {step === "confirm" && <Appbar.BackAction onPress={handleBackPress} />}
        <Appbar.Content title="Setup PIN" />
      </Appbar.Header>

      <View style={styles.content}>
        <View style={styles.headerContainer}>
          <Text variant="headlineMedium" style={styles.title}>
            {step === "setup" ? "Create Your PIN" : "Confirm Your PIN"}
          </Text>
          <Text variant="bodyLarge" style={styles.subtitle}>
            {step === "setup"
              ? "Choose a 4-6 digit PIN to secure your photo vault"
              : "Enter your PIN again to confirm"}
          </Text>
        </View>

        <PinInput
          pin={getCurrentPin()}
          onPinChange={handlePinChange}
          onSubmit={handlePinSubmit}
          isLoading={isLoading}
          error={error}
        />

        <View style={styles.infoContainer}>
          <Text variant="bodySmall" style={styles.infoText}>
            🔒 Your PIN is stored securely on your device
          </Text>
          <Text variant="bodySmall" style={styles.infoText}>
            📱 All photos are saved locally and never leave your device
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#f5f5f5",
  },
  content: {
    flex: 1,
    justifyContent: "center",
    padding: 20,
  },
  headerContainer: {
    alignItems: "center",
    marginBottom: 40,
  },
  title: {
    textAlign: "center",
    marginBottom: 10,
    fontWeight: "bold",
  },
  subtitle: {
    textAlign: "center",
    color: "#666",
    paddingHorizontal: 20,
  },
  infoContainer: {
    marginTop: 40,
    alignItems: "center",
  },
  infoText: {
    textAlign: "center",
    color: "#888",
    marginBottom: 5,
  },
});

export default PinSetupScreen;
