import React, { useState } from 'react';
import { View, StyleSheet } from 'react-native';
import { Text, Appbar } from 'react-native-paper';
import { StackNavigationProp } from '@react-navigation/stack';
import PinInput from '../components/PinInput';
import StorageService from '../services/StorageService';
import { RootStackParamList } from '../types';

type PinLoginScreenNavigationProp = StackNavigationProp<RootStackParamList, 'PinLogin'>;

interface Props {
  navigation: PinLoginScreenNavigationProp;
}

const PinLoginScreen: React.FC<Props> = ({ navigation }) => {
  const [pin, setPin] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState('');
  const [attemptCount, setAttemptCount] = useState(0);

  const handlePinSubmit = async () => {
    if (pin.length === 4) {
      setIsLoading(true);
      
      // Add a small delay to show loading state
      setTimeout(() => {
        const isValid = StorageService.verifyPIN(pin);
        
        if (isValid) {
          // PIN is correct, navigate to gallery
          navigation.replace('Gallery');
        } else {
          // PIN is incorrect
          const newAttemptCount = attemptCount + 1;
          setAttemptCount(newAttemptCount);
          
          if (newAttemptCount >= 5) {
            setError('Too many failed attempts. Please try again later.');
          } else {
            setError(`Incorrect PIN. ${5 - newAttemptCount} attempts remaining.`);
          }
          
          setPin('');
        }
        
        setIsLoading(false);
      }, 500);
    }
  };

  const handlePinChange = (newPin: string) => {
    setError('');
    setPin(newPin);
  };

  return (
    <View style={styles.container}>
      <Appbar.Header>
        <Appbar.Content title="PhotoVault" />
      </Appbar.Header>

      <View style={styles.content}>
        <View style={styles.headerContainer}>
          <Text variant="displaySmall" style={styles.appTitle}>
            📸 PhotoVault
          </Text>
          <Text variant="headlineMedium" style={styles.title}>
            Enter Your PIN
          </Text>
          <Text variant="bodyLarge" style={styles.subtitle}>
            Enter your 4-digit PIN to access your secure photo vault
          </Text>
        </View>

        <PinInput
          pin={pin}
          onPinChange={handlePinChange}
          onSubmit={handlePinSubmit}
          isLoading={isLoading}
          error={error}
        />

        <View style={styles.infoContainer}>
          <Text variant="bodySmall" style={styles.infoText}>
            🔒 Your photos are stored securely on this device
          </Text>
        </View>
      </View>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  content: {
    flex: 1,
    justifyContent: 'center',
    padding: 20,
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  appTitle: {
    textAlign: 'center',
    marginBottom: 20,
    fontWeight: 'bold',
    color: '#6200ea',
  },
  title: {
    textAlign: 'center',
    marginBottom: 10,
    fontWeight: 'bold',
  },
  subtitle: {
    textAlign: 'center',
    color: '#666',
    paddingHorizontal: 20,
  },
  infoContainer: {
    marginTop: 40,
    alignItems: 'center',
  },
  infoText: {
    textAlign: 'center',
    color: '#888',
  },
});

export default PinLoginScreen;
