{"version": 3, "names": ["_hammerjs", "_interopRequireDefault", "require", "_State", "e", "__esModule", "default", "CONTENT_TOUCHES_DELAY", "exports", "CONTENT_TOUCHES_QUICK_TAP_END_DELAY", "MULTI_FINGER_PAN_MAX_PINCH_THRESHOLD", "MULTI_FINGER_PAN_MAX_ROTATION_THRESHOLD", "DEG_RAD", "Math", "PI", "EventMap", "Hammer", "INPUT_START", "State", "BEGAN", "INPUT_MOVE", "ACTIVE", "INPUT_END", "END", "INPUT_CANCEL", "FAILED", "Direction", "RIGHT", "LEFT", "UP", "DOWN", "DirectionMap", "DIRECTION_RIGHT", "DIRECTION_LEFT", "DIRECTION_UP", "DIRECTION_DOWN", "HammerInputNames", "HammerDirectionNames", "DIRECTION_HORIZONTAL", "DIRECTION_VERTICAL", "DIRECTION_NONE", "DIRECTION_ALL"], "sourceRoot": "../../../src", "sources": ["web_hammer/constants.ts"], "mappings": ";;;;;;AAAA,IAAAA,SAAA,GAAAC,sBAAA,CAAAC,OAAA;AAEA,IAAAC,MAAA,GAAAD,OAAA;AAAiC,SAAAD,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE1B,MAAMG,qBAAqB,GAAAC,OAAA,CAAAD,qBAAA,GAAG,GAAG;AACjC,MAAME,mCAAmC,GAAAD,OAAA,CAAAC,mCAAA,GAAG,EAAE;AAC9C,MAAMC,oCAAoC,GAAAF,OAAA,CAAAE,oCAAA,GAAG,GAAG;AAChD,MAAMC,uCAAuC,GAAAH,OAAA,CAAAG,uCAAA,GAAG,CAAC;AACjD,MAAMC,OAAO,GAAAJ,OAAA,CAAAI,OAAA,GAAGC,IAAI,CAACC,EAAE,GAAG,GAAG;;AAEpC;AACO,MAAMC,QAAQ,GAAAP,OAAA,CAAAO,QAAA,GAAG;EACtB,CAACC,iBAAM,CAACC,WAAW,GAAGC,YAAK,CAACC,KAAK;EACjC,CAACH,iBAAM,CAACI,UAAU,GAAGF,YAAK,CAACG,MAAM;EACjC,CAACL,iBAAM,CAACM,SAAS,GAAGJ,YAAK,CAACK,GAAG;EAC7B,CAACP,iBAAM,CAACQ,YAAY,GAAGN,YAAK,CAACO;AAC/B,CAAU;AAEH,MAAMC,SAAS,GAAAlB,OAAA,CAAAkB,SAAA,GAAG;EACvBC,KAAK,EAAE,CAAC;EACRC,IAAI,EAAE,CAAC;EACPC,EAAE,EAAE,CAAC;EACLC,IAAI,EAAE;AACR,CAAC;AAEM,MAAMC,YAAY,GAAAvB,OAAA,CAAAuB,YAAA,GAAG;EAC1B,CAACf,iBAAM,CAACgB,eAAe,GAAGN,SAAS,CAACC,KAAK;EACzC,CAACX,iBAAM,CAACiB,cAAc,GAAGP,SAAS,CAACE,IAAI;EACvC,CAACZ,iBAAM,CAACkB,YAAY,GAAGR,SAAS,CAACG,EAAE;EACnC,CAACb,iBAAM,CAACmB,cAAc,GAAGT,SAAS,CAACI;AACrC,CAAC;AAEM,MAAMM,gBAAgB,GAAA5B,OAAA,CAAA4B,gBAAA,GAAG;EAC9B,CAACpB,iBAAM,CAACC,WAAW,GAAG,OAAO;EAC7B,CAACD,iBAAM,CAACI,UAAU,GAAG,MAAM;EAC3B,CAACJ,iBAAM,CAACM,SAAS,GAAG,KAAK;EACzB,CAACN,iBAAM,CAACQ,YAAY,GAAG;AACzB,CAAC;AACM,MAAMa,oBAAoB,GAAA7B,OAAA,CAAA6B,oBAAA,GAAG;EAClC,CAACrB,iBAAM,CAACsB,oBAAoB,GAAG,YAAY;EAC3C,CAACtB,iBAAM,CAACkB,YAAY,GAAG,IAAI;EAC3B,CAAClB,iBAAM,CAACmB,cAAc,GAAG,MAAM;EAC/B,CAACnB,iBAAM,CAACuB,kBAAkB,GAAG,UAAU;EACvC,CAACvB,iBAAM,CAACwB,cAAc,GAAG,MAAM;EAC/B,CAACxB,iBAAM,CAACyB,aAAa,GAAG,KAAK;EAC7B,CAACzB,iBAAM,CAACgB,eAAe,GAAG,OAAO;EACjC,CAAChB,iBAAM,CAACiB,cAAc,GAAG;AAC3B,CAAC", "ignoreList": []}