{"expo": {"name": "PhotoVault", "slug": "PhotoVault", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/icon.png", "userInterfaceStyle": "light", "newArchEnabled": true, "splash": {"image": "./assets/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#6200ea"}, "assetBundlePatterns": ["**/*"], "ios": {"supportsTablet": true, "infoPlist": {"NSCameraUsageDescription": "PhotoVault needs camera access to take photos for your secure vault.", "NSPhotoLibraryUsageDescription": "PhotoVault needs photo library access to import existing photos to your secure vault."}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/adaptive-icon.png", "backgroundColor": "#6200ea"}, "edgeToEdgeEnabled": true, "permissions": ["android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"]}, "web": {"favicon": "./assets/favicon.png"}, "plugins": [["expo-camera", {"cameraPermission": "PhotoVault needs camera access to take photos for your secure vault."}], ["expo-image-picker", {"photosPermission": "PhotoVault needs photo library access to import existing photos to your secure vault."}]]}}