{"version": 3, "names": ["createMMKV", "createMockMMKV", "isTest", "addMemoryWarningListener", "onValueChangedListeners", "Map", "MMKV", "constructor", "configuration", "id", "nativeInstance", "functionCache", "has", "set", "get", "getFunctionFromCache", "functionName", "onValuesChanged", "keys", "length", "key", "listener", "size", "isReadOnly", "value", "func", "getBoolean", "getString", "getNumber", "<PERSON><PERSON><PERSON><PERSON>", "contains", "delete", "getAllKeys", "clearAll", "recrypt", "trim", "toString", "join", "toJSON", "addOnValueChangedListener", "onValueChanged", "push", "remove", "index", "indexOf", "splice"], "sourceRoot": "../../src", "sources": ["MMKV.ts"], "mappings": ";;AAAA,SAASA,UAAU,QAAQ,cAAc;AACzC,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,MAAM,QAAQ,mBAAmB;AAO1C,SAASC,wBAAwB,QAAQ,yBAAyB;AAElE,MAAMC,uBAAuB,GAAG,IAAIC,GAAG,CAAoC,CAAC;;AAE5E;AACA;AACA;AACA,OAAO,MAAMC,IAAI,CAA0B;EAKzC;AACF;AACA;AACA;EACEC,WAAWA,CAACC,aAA4B,GAAG;IAAEC,EAAE,EAAE;EAAe,CAAC,EAAE;IACjE,IAAI,CAACA,EAAE,GAAGD,aAAa,CAACC,EAAE;IAC1B,IAAI,CAACC,cAAc,GAAGR,MAAM,CAAC,CAAC,GAC1BD,cAAc,CAAC,CAAC,GAChBD,UAAU,CAACQ,aAAa,CAAC;IAC7B,IAAI,CAACG,aAAa,GAAG,CAAC,CAAC;IAEvBR,wBAAwB,CAAC,IAAI,CAAC;EAChC;EAEA,IAAYC,uBAAuBA,CAAA,EAAG;IACpC,IAAI,CAACA,uBAAuB,CAACQ,GAAG,CAAC,IAAI,CAACH,EAAE,CAAC,EAAE;MACzCL,uBAAuB,CAACS,GAAG,CAAC,IAAI,CAACJ,EAAE,EAAE,EAAE,CAAC;IAC1C;IACA,OAAOL,uBAAuB,CAACU,GAAG,CAAC,IAAI,CAACL,EAAE,CAAC;EAC7C;EAEQM,oBAAoBA,CAC1BC,YAAe,EACA;IACf,IAAI,IAAI,CAACL,aAAa,CAACK,YAAY,CAAC,IAAI,IAAI,EAAE;MAC5C,IAAI,CAACL,aAAa,CAACK,YAAY,CAAC,GAAG,IAAI,CAACN,cAAc,CAACM,YAAY,CAAC;IACtE;IACA,OAAO,IAAI,CAACL,aAAa,CAACK,YAAY,CAAC;EACzC;EAEQC,eAAeA,CAACC,IAAc,EAAE;IACtC,IAAI,IAAI,CAACd,uBAAuB,CAACe,MAAM,KAAK,CAAC,EAAE;IAE/C,KAAK,MAAMC,GAAG,IAAIF,IAAI,EAAE;MACtB,KAAK,MAAMG,QAAQ,IAAI,IAAI,CAACjB,uBAAuB,EAAE;QACnDiB,QAAQ,CAACD,GAAG,CAAC;MACf;IACF;EACF;EAEA,IAAIE,IAAIA,CAAA,EAAW;IACjB,OAAO,IAAI,CAACZ,cAAc,CAACY,IAAI;EACjC;EACA,IAAIC,UAAUA,CAAA,EAAY;IACxB,OAAO,IAAI,CAACb,cAAc,CAACa,UAAU;EACvC;EACAV,GAAGA,CAACO,GAAW,EAAEI,KAA8C,EAAQ;IACrE,MAAMC,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,KAAK,CAAC;IAC7CU,IAAI,CAACL,GAAG,EAAEI,KAAK,CAAC;IAEhB,IAAI,CAACP,eAAe,CAAC,CAACG,GAAG,CAAC,CAAC;EAC7B;EACAM,UAAUA,CAACN,GAAW,EAAuB;IAC3C,MAAMK,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,YAAY,CAAC;IACpD,OAAOU,IAAI,CAACL,GAAG,CAAC;EAClB;EACAO,SAASA,CAACP,GAAW,EAAsB;IACzC,MAAMK,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,WAAW,CAAC;IACnD,OAAOU,IAAI,CAACL,GAAG,CAAC;EAClB;EACAQ,SAASA,CAACR,GAAW,EAAsB;IACzC,MAAMK,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,WAAW,CAAC;IACnD,OAAOU,IAAI,CAACL,GAAG,CAAC;EAClB;EACAS,SAASA,CAACT,GAAW,EAA+B;IAClD,MAAMK,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,WAAW,CAAC;IACnD,OAAOU,IAAI,CAACL,GAAG,CAAC;EAClB;EACAU,QAAQA,CAACV,GAAW,EAAW;IAC7B,MAAMK,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,UAAU,CAAC;IAClD,OAAOU,IAAI,CAACL,GAAG,CAAC;EAClB;EACAW,MAAMA,CAACX,GAAW,EAAQ;IACxB,MAAMK,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,QAAQ,CAAC;IAChDU,IAAI,CAACL,GAAG,CAAC;IAET,IAAI,CAACH,eAAe,CAAC,CAACG,GAAG,CAAC,CAAC;EAC7B;EACAY,UAAUA,CAAA,EAAa;IACrB,MAAMP,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,YAAY,CAAC;IACpD,OAAOU,IAAI,CAAC,CAAC;EACf;EACAQ,QAAQA,CAAA,EAAS;IACf,MAAMf,IAAI,GAAG,IAAI,CAACc,UAAU,CAAC,CAAC;IAE9B,MAAMP,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,UAAU,CAAC;IAClDU,IAAI,CAAC,CAAC;IAEN,IAAI,CAACR,eAAe,CAACC,IAAI,CAAC;EAC5B;EACAgB,OAAOA,CAACd,GAAuB,EAAQ;IACrC,MAAMK,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,SAAS,CAAC;IACjD,OAAOU,IAAI,CAACL,GAAG,CAAC;EAClB;EACAe,IAAIA,CAAA,EAAS;IACX,MAAMV,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,MAAM,CAAC;IAC9CU,IAAI,CAAC,CAAC;EACR;EAEAW,QAAQA,CAAA,EAAW;IACjB,OAAO,SAAS,IAAI,CAAC3B,EAAE,OAAO,IAAI,CAACuB,UAAU,CAAC,CAAC,CAACK,IAAI,CAAC,IAAI,CAAC,GAAG;EAC/D;EACAC,MAAMA,CAAA,EAAW;IACf,OAAO;MACL,CAAC,IAAI,CAAC7B,EAAE,GAAG,IAAI,CAACuB,UAAU,CAAC;IAC7B,CAAC;EACH;EAEAO,yBAAyBA,CAACC,cAAqC,EAAY;IACzE,IAAI,CAACpC,uBAAuB,CAACqC,IAAI,CAACD,cAAc,CAAC;IAEjD,OAAO;MACLE,MAAM,EAAEA,CAAA,KAAM;QACZ,MAAMC,KAAK,GAAG,IAAI,CAACvC,uBAAuB,CAACwC,OAAO,CAACJ,cAAc,CAAC;QAClE,IAAIG,KAAK,KAAK,CAAC,CAAC,EAAE;UAChB,IAAI,CAACvC,uBAAuB,CAACyC,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QAC/C;MACF;IACF,CAAC;EACH;AACF", "ignoreList": []}