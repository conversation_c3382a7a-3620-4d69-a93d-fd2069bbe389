{"version": 3, "names": ["Circular<PERSON><PERSON>er", "constructor", "size", "capacity", "buffer", "Array", "index", "_size", "push", "element", "Math", "min", "get", "at", "clear", "exports", "default"], "sourceRoot": "../../../../src", "sources": ["web/tools/CircularBuffer.ts"], "mappings": ";;;;;;AAAe,MAAMA,cAAc,CAAI;EAMrCC,WAAWA,CAACC,IAAY,EAAE;IACxB,IAAI,CAACC,QAAQ,GAAGD,IAAI;IACpB,IAAI,CAACE,MAAM,GAAG,IAAIC,KAAK,CAAIH,IAAI,CAAC;IAChC,IAAI,CAACI,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,KAAK,GAAG,CAAC;EAChB;EAEOC,IAAIA,CAACC,OAAU,EAAQ;IAC5B,IAAI,CAACL,MAAM,CAAC,IAAI,CAACE,KAAK,CAAC,GAAGG,OAAO;IACjC,IAAI,CAACH,KAAK,GAAG,CAAC,IAAI,CAACA,KAAK,GAAG,CAAC,IAAI,IAAI,CAACH,QAAQ;IAC7C,IAAI,CAACI,KAAK,GAAGG,IAAI,CAACC,GAAG,CAAC,IAAI,CAACT,IAAI,GAAG,CAAC,EAAE,IAAI,CAACC,QAAQ,CAAC;EACrD;EAEOS,GAAGA,CAACC,EAAU,EAAK;IACxB,IAAI,IAAI,CAACN,KAAK,KAAK,IAAI,CAACJ,QAAQ,EAAE;MAChC,IAAIG,KAAK,GAAG,CAAC,IAAI,CAACA,KAAK,GAAGO,EAAE,IAAI,IAAI,CAACV,QAAQ;MAC7C,IAAIG,KAAK,GAAG,CAAC,EAAE;QACbA,KAAK,IAAI,IAAI,CAACH,QAAQ;MACxB;MAEA,OAAO,IAAI,CAACC,MAAM,CAACE,KAAK,CAAC;IAC3B,CAAC,MAAM;MACL,OAAO,IAAI,CAACF,MAAM,CAACS,EAAE,CAAC;IACxB;EACF;EAEOC,KAAKA,CAAA,EAAS;IACnB,IAAI,CAACV,MAAM,GAAG,IAAIC,KAAK,CAAI,IAAI,CAACF,QAAQ,CAAC;IACzC,IAAI,CAACG,KAAK,GAAG,CAAC;IACd,IAAI,CAACC,KAAK,GAAG,CAAC;EAChB;EAEA,IAAWL,IAAIA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACK,KAAK;EACnB;AACF;AAACQ,OAAA,CAAAC,OAAA,GAAAhB,cAAA", "ignoreList": []}