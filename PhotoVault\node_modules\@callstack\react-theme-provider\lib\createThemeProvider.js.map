{"version": 3, "sources": ["../src/createThemeProvider.js"], "names": ["createThemeProvider", "defaultTheme", "ThemeContext", "render", "props", "theme", "children", "React", "Component"], "mappings": ";;;;;AAEA;;;;;;;;AAOA,SAASA,mBAAT,CACEC,YADF,EAEEC,YAFF,EAGwB;AAAA;;AACtB;AAAA;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;;AAAA,WAKEC,MALF,GAKE,kBAAS;AACP,aACE,oBAAC,YAAD,CAAc,QAAd;AAAuB,QAAA,KAAK,EAAE,KAAKC,KAAL,CAAWC;AAAzC,SACG,KAAKD,KAAL,CAAWE,QADd,CADF;AAKD,KAXH;;AAAA;AAAA,IAAmCC,KAAK,CAACC,SAAzC,2CACwB;AACpBH,IAAAA,KAAK,EAAEJ;AADa,GADxB;AAaD;;eAEcD,mB", "sourcesContent": ["/* @flow */\n\nimport * as React from 'react';\n\nexport type ThemeProviderType<T> = React.ComponentType<{\n  children: React.Node,\n  theme?: T,\n}>;\n\nfunction createThemeProvider<T>(\n  defaultTheme: T,\n  ThemeContext: React.Context<T>\n): ThemeProviderType<T> {\n  return class ThemeProvider extends React.Component<*> {\n    static defaultProps = {\n      theme: defaultTheme,\n    };\n\n    render() {\n      return (\n        <ThemeContext.Provider value={this.props.theme}>\n          {this.props.children}\n        </ThemeContext.Provider>\n      );\n    }\n  };\n}\n\nexport default createThemeProvider;\n"], "file": "createThemeProvider.js"}