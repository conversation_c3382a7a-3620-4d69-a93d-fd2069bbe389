{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_utils", "_theming", "_MaterialCommunityIcon", "_interopRequireDefault", "_TouchableRipple", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "CheckboxIOS", "status", "disabled", "onPress", "theme", "themeOverrides", "testID", "rest", "useInternalTheme", "checked", "indeterminate", "checkedColor", "rippleColor", "getSelectionControlIOSColor", "customColor", "color", "icon", "opacity", "createElement", "borderless", "accessibilityRole", "accessibilityState", "accessibilityLiveRegion", "style", "styles", "container", "View", "allowFontScaling", "name", "size", "direction", "exports", "displayName", "StyleSheet", "create", "borderRadius", "padding", "_default"], "sourceRoot": "../../../../src", "sources": ["components/Checkbox/CheckboxIOS.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,QAAA,GAAAH,OAAA;AAEA,IAAAI,sBAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,gBAAA,GAAAD,sBAAA,CAAAL,OAAA;AAAiE,SAAAK,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAZ,uBAAA,YAAAA,CAAAQ,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAAA,SAAAgB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAf,CAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAK,CAAA,IAAAF,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAZ,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAa,QAAA,CAAAK,KAAA,OAAAF,SAAA;AA6BjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,WAAW,GAAGA,CAAC;EACnBC,MAAM;EACNC,QAAQ;EACRC,OAAO;EACPC,KAAK,EAAEC,cAAc;EACrBC,MAAM;EACN,GAAGC;AACE,CAAC,KAAK;EACX,MAAMH,KAAK,GAAG,IAAAI,yBAAgB,EAACH,cAAc,CAAC;EAC9C,MAAMI,OAAO,GAAGR,MAAM,KAAK,SAAS;EACpC,MAAMS,aAAa,GAAGT,MAAM,KAAK,eAAe;EAEhD,MAAM;IAAEU,YAAY;IAAEC;EAAY,CAAC,GAAG,IAAAC,kCAA2B,EAAC;IAChET,KAAK;IACLF,QAAQ;IACRY,WAAW,EAAEP,IAAI,CAACQ;EACpB,CAAC,CAAC;EAEF,MAAMC,IAAI,GAAGN,aAAa,GAAG,OAAO,GAAG,OAAO;EAC9C,MAAMO,OAAO,GAAGP,aAAa,IAAID,OAAO,GAAG,CAAC,GAAG,CAAC;EAEhD,oBACE3C,KAAA,CAAAoD,aAAA,CAAC5C,gBAAA,CAAAG,OAAe,EAAAiB,QAAA,KACVa,IAAI;IACRY,UAAU;IACVP,WAAW,EAAEA,WAAY;IACzBT,OAAO,EAAEA,OAAQ;IACjBD,QAAQ,EAAEA,QAAS;IACnBkB,iBAAiB,EAAC,UAAU;IAC5BC,kBAAkB,EAAE;MAAEnB,QAAQ;MAAEO;IAAQ,CAAE;IAC1Ca,uBAAuB,EAAC,QAAQ;IAChCC,KAAK,EAAEC,MAAM,CAACC,SAAU;IACxBnB,MAAM,EAAEA,MAAO;IACfF,KAAK,EAAEA;EAAM,iBAEbtC,KAAA,CAAAoD,aAAA,CAACjD,YAAA,CAAAyD,IAAI;IAACH,KAAK,EAAE;MAAEN;IAAQ;EAAE,gBACvBnD,KAAA,CAAAoD,aAAA,CAAC9C,sBAAA,CAAAK,OAAqB;IACpBkD,gBAAgB,EAAE,KAAM;IACxBC,IAAI,EAAEZ,IAAK;IACXa,IAAI,EAAE,EAAG;IACTd,KAAK,EAAEJ,YAAa;IACpBmB,SAAS,EAAC;EAAK,CAChB,CACG,CACS,CAAC;AAEtB,CAAC;AAACC,OAAA,CAAA/B,WAAA,GAAAA,WAAA;AAEFA,WAAW,CAACgC,WAAW,GAAG,cAAc;AAExC,MAAMR,MAAM,GAAGS,uBAAU,CAACC,MAAM,CAAC;EAC/BT,SAAS,EAAE;IACTU,YAAY,EAAE,EAAE;IAChBC,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AAAC,IAAAC,QAAA,GAAAN,OAAA,CAAAtD,OAAA,GAEYuB,WAAW,EAE1B", "ignoreList": []}