{"version": 3, "names": ["TurboModuleRegistry", "ModuleNotFoundError", "getMMKVPlatformContextTurboModule", "Mode", "mmkvModule", "getMMKVTurboModule", "getEnforcing", "platformContext", "basePath", "getBaseDirectory", "initialize", "cause"], "sourceRoot": "../../src", "sources": ["NativeMmkv.ts"], "mappings": ";;AACA,SAASA,mBAAmB,QAAQ,cAAc;AAElD,SAASC,mBAAmB,QAAQ,uBAAuB;AAC3D,SAASC,iCAAiC,QAAQ,6BAA6B;;AAE/E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,WAAYC,IAAI,0BAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAJA,IAAI,CAAJA,IAAI;EAAA,OAAJA,IAAI;AAAA;;AAWhB;AACA;AACA;;AAqEA,IAAIC,UAAuB;AAE3B,OAAO,SAASC,kBAAkBA,CAAA,EAAS;EACzC,IAAI;IACF,IAAID,UAAU,IAAI,IAAI,EAAE;MACtB;MACAA,UAAU,GAAGJ,mBAAmB,CAACM,YAAY,CAAO,SAAS,CAAC;;MAE9D;MACA,MAAMC,eAAe,GAAGL,iCAAiC,CAAC,CAAC;;MAE3D;MACA,MAAMM,QAAQ,GAAGD,eAAe,CAACE,gBAAgB,CAAC,CAAC;MACnDL,UAAU,CAACM,UAAU,CAACF,QAAQ,CAAC;IACjC;IAEA,OAAOJ,UAAU;EACnB,CAAC,CAAC,OAAOO,KAAK,EAAE;IACd;IACA,MAAM,IAAIV,mBAAmB,CAACU,KAAK,CAAC;EACtC;AACF", "ignoreList": []}