export interface ImageItem {
  id: string;
  uri: string;
  filename: string;
  createdAt: number;
  size?: number;
}

export interface StorageKeys {
  PIN: string;
  IMAGES: string;
  IS_FIRST_LAUNCH: string;
}

export const STORAGE_KEYS: StorageKeys = {
  PIN: 'user_pin',
  IMAGES: 'vault_images',
  IS_FIRST_LAUNCH: 'is_first_launch',
};

export type RootStackParamList = {
  PinSetup: undefined;
  PinLogin: undefined;
  Gallery: undefined;
  Camera: undefined;
};

export interface PinInputProps {
  pin: string;
  onPinChange: (pin: string) => void;
  onSubmit: () => void;
  isLoading?: boolean;
  error?: string;
}

export interface GalleryItemProps {
  item: ImageItem;
  onPress: (item: ImageItem) => void;
  onLongPress?: (item: ImageItem) => void;
}
