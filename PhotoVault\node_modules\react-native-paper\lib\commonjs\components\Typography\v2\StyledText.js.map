{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_color", "_interopRequireDefault", "_Text", "_theming", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "StyledText", "alpha", "family", "style", "theme", "themeOverrides", "rest", "_theme$colors", "_theme$fonts", "useInternalTheme", "textColor", "color", "isV3", "colors", "onSurface", "text", "rgb", "string", "writingDirection", "I18nManager", "getConstants", "isRTL", "createElement", "styles", "fonts", "StyleSheet", "create", "textAlign", "_default", "exports"], "sourceRoot": "../../../../../src", "sources": ["components/Typography/v2/StyledText.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AAEA,IAAAE,MAAA,GAAAC,sBAAA,CAAAH,OAAA;AAGA,IAAAI,KAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,QAAA,GAAAL,OAAA;AAAyD,SAAAG,uBAAAG,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAP,wBAAAO,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAX,uBAAA,YAAAA,CAAAO,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAAA,SAAAgB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAf,CAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAK,CAAA,IAAAF,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAZ,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAa,QAAA,CAAAK,KAAA,OAAAF,SAAA;AASzD,MAAMG,UAAU,GAAGA,CAAC;EAClBC,KAAK,GAAG,CAAC;EACTC,MAAM;EACNC,KAAK;EACLC,KAAK,EAAEC,cAAc;EACrB,GAAGC;AACE,CAAC,KAAK;EAAA,IAAAC,aAAA,EAAAC,YAAA;EACX,MAAMJ,KAAK,GAAG,IAAAK,yBAAgB,EAACJ,cAAc,CAAC;EAE9C,MAAMK,SAAS,GAAG,IAAAC,cAAK,EACrBP,KAAK,CAACQ,IAAI,GAAGR,KAAK,CAACS,MAAM,CAACC,SAAS,IAAAP,aAAA,GAAGH,KAAK,CAACS,MAAM,cAAAN,aAAA,uBAAZA,aAAA,CAAcQ,IACtD,CAAC,CACEd,KAAK,CAACA,KAAK,CAAC,CACZe,GAAG,CAAC,CAAC,CACLC,MAAM,CAAC,CAAC;EACX,MAAMC,gBAAgB,GAAGC,wBAAW,CAACC,YAAY,CAAC,CAAC,CAACC,KAAK,GAAG,KAAK,GAAG,KAAK;EAEzE,oBACEtD,KAAA,CAAAuD,aAAA,CAACjD,KAAA,CAAAI,OAAI,EAAAiB,QAAA,KACCY,IAAI;IACRH,KAAK,EAAE,CACLoB,MAAM,CAACR,IAAI,EACX;MACEJ,KAAK,EAAED,SAAS;MAChB,IAAI,CAACN,KAAK,CAACQ,IAAI,MAAAJ,YAAA,GAAIJ,KAAK,CAACoB,KAAK,cAAAhB,YAAA,uBAAXA,YAAA,CAAcN,MAAM,CAAC,EAAC;MACzCgB;IACF,CAAC,EACDf,KAAK;EACL,EACH,CAAC;AAEN,CAAC;AAED,MAAMoB,MAAM,GAAGE,uBAAU,CAACC,MAAM,CAAC;EAC/BX,IAAI,EAAE;IACJY,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAApD,OAAA,GAEYuB,UAAU", "ignoreList": []}