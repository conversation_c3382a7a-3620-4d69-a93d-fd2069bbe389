{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_theming", "_forwardRef", "e", "t", "WeakMap", "r", "n", "__esModule", "o", "i", "f", "__proto__", "default", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "Text", "style", "theme", "overrideTheme", "rest", "ref", "_theme$fonts", "_theme$colors", "root", "useRef", "useInternalTheme", "useImperativeHandle", "setNativeProps", "args", "_root$current", "current", "createElement", "isV3", "fonts", "regular", "color", "colors", "onSurface", "text", "styles", "StyleSheet", "create", "textAlign", "_default", "exports", "forwardRef"], "sourceRoot": "../../../../../src", "sources": ["components/Typography/v2/Text.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AASA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,WAAA,GAAAH,OAAA;AAAuD,SAAAD,wBAAAK,CAAA,EAAAC,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAP,uBAAA,YAAAA,CAAAK,CAAA,EAAAC,CAAA,SAAAA,CAAA,IAAAD,CAAA,IAAAA,CAAA,CAAAK,UAAA,SAAAL,CAAA,MAAAM,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAC,OAAA,EAAAV,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAQ,CAAA,MAAAF,CAAA,GAAAL,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAG,CAAA,CAAAK,GAAA,CAAAX,CAAA,UAAAM,CAAA,CAAAM,GAAA,CAAAZ,CAAA,GAAAM,CAAA,CAAAO,GAAA,CAAAb,CAAA,EAAAQ,CAAA,gBAAAP,CAAA,IAAAD,CAAA,gBAAAC,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAC,CAAA,OAAAM,CAAA,IAAAD,CAAA,GAAAU,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAC,CAAA,OAAAM,CAAA,CAAAK,GAAA,IAAAL,CAAA,CAAAM,GAAA,IAAAP,CAAA,CAAAE,CAAA,EAAAP,CAAA,EAAAM,CAAA,IAAAC,CAAA,CAAAP,CAAA,IAAAD,CAAA,CAAAC,CAAA,WAAAO,CAAA,KAAAR,CAAA,EAAAC,CAAA;AAAA,SAAAkB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAjB,CAAA,aAAAJ,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAC,CAAA,GAAAqB,SAAA,CAAAtB,CAAA,YAAAG,CAAA,IAAAF,CAAA,OAAAa,cAAA,CAAAC,IAAA,CAAAd,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAe,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAUvD;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMG,IAA+C,GAAGA,CACtD;EAAEC,KAAK;EAAEC,KAAK,EAAEC,aAAa;EAAE,GAAGC;AAAY,CAAC,EAC/CC,GAAG,KACA;EAAA,IAAAC,YAAA,EAAAC,aAAA;EACH,MAAMC,IAAI,GAAGvC,KAAK,CAACwC,MAAM,CAAoB,IAAI,CAAC;EAClD,MAAMP,KAAK,GAAG,IAAAQ,yBAAgB,EAACP,aAAa,CAAC;EAE7ClC,KAAK,CAAC0C,mBAAmB,CAACN,GAAG,EAAE,OAAO;IACpCO,cAAc,EAAGC,IAAY;MAAA,IAAAC,aAAA;MAAA,QAAAA,aAAA,GAAKN,IAAI,CAACO,OAAO,cAAAD,aAAA,uBAAZA,aAAA,CAAcF,cAAc,CAACC,IAAI,CAAC;IAAA;EACtE,CAAC,CAAC,CAAC;EAEH,oBACE5C,KAAA,CAAA+C,aAAA,CAAC5C,YAAA,CAAA4B,IAAU,EAAAN,QAAA,KACLU,IAAI;IACRC,GAAG,EAAEG,IAAK;IACVP,KAAK,EAAE,CACL;MACE,IAAI,CAACC,KAAK,CAACe,IAAI,MAAAX,YAAA,GAAIJ,KAAK,CAACgB,KAAK,cAAAZ,YAAA,uBAAXA,YAAA,CAAaa,OAAO,EAAC;MACxCC,KAAK,EAAElB,KAAK,CAACe,IAAI,IAAAV,aAAA,GAAGL,KAAK,CAACmB,MAAM,cAAAd,aAAA,uBAAZA,aAAA,CAAce,SAAS,GAAGpB,KAAK,CAACmB,MAAM,CAACE;IAC7D,CAAC,EACDC,MAAM,CAACD,IAAI,EACXtB,KAAK;EACL,EACH,CAAC;AAEN,CAAC;AAED,MAAMuB,MAAM,GAAGC,uBAAU,CAACC,MAAM,CAAC;EAC/BH,IAAI,EAAE;IACJI,SAAS,EAAE;EACb;AACF,CAAC,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAA5C,OAAA,GAEY,IAAA6C,sBAAU,EAAC9B,IAAI,CAAC", "ignoreList": []}