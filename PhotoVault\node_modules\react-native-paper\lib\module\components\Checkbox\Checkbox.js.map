{"version": 3, "names": ["React", "Platform", "CheckboxAndroid", "CheckboxIOS", "useInternalTheme", "Checkbox", "theme", "themeOverrides", "props", "OS", "createElement", "_extends", "CheckboxWithTheme"], "sourceRoot": "../../../../src", "sources": ["components/Checkbox/Checkbox.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAAgCC,QAAQ,QAAQ,cAAc;AAE9D,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,gBAAgB,QAAQ,oBAAoB;AAkCrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,QAAQ,GAAGA,CAAC;EAAEC,KAAK,EAAEC,cAAc;EAAE,GAAGC;AAAa,CAAC,KAAK;EAC/D,MAAMF,KAAK,GAAGF,gBAAgB,CAACG,cAAc,CAAC;EAC9C,OAAON,QAAQ,CAACQ,EAAE,KAAK,KAAK,gBAC1BT,KAAA,CAAAU,aAAA,CAACP,WAAW,EAAAQ,QAAA,KAAKH,KAAK;IAAEF,KAAK,EAAEA;EAAM,EAAE,CAAC,gBAExCN,KAAA,CAAAU,aAAA,CAACR,eAAe,EAAAS,QAAA,KAAKH,KAAK;IAAEF,KAAK,EAAEA;EAAM,EAAE,CAC5C;AACH,CAAC;AAED,eAAeD,QAAQ;;AAEvB;AACA,MAAMO,iBAAiB,GAAGP,QAAQ;AAClC;AACA,SAASO,iBAAiB,IAAIP,QAAQ", "ignoreList": []}