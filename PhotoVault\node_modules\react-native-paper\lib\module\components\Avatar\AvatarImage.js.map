{"version": 3, "names": ["React", "Image", "StyleSheet", "View", "useInternalTheme", "defaultSize", "AvatarImage", "size", "source", "style", "onError", "onLayout", "onLoad", "onLoadEnd", "onLoadStart", "onProgress", "theme", "themeOverrides", "testID", "rest", "colors", "backgroundColor", "primary", "flatten", "createElement", "_extends", "width", "height", "borderRadius", "accessibilityIgnoresInvertColors", "displayName"], "sourceRoot": "../../../../src", "sources": ["components/Avatar/AvatarImage.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SACEC,KAAK,EAILC,UAAU,EACVC,IAAI,QAEC,cAAc;AAErB,SAASC,gBAAgB,QAAQ,oBAAoB;AAGrD,MAAMC,WAAW,GAAG,EAAE;AAgDtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAGA,CAAC;EACnBC,IAAI,GAAGF,WAAW;EAClBG,MAAM;EACNC,KAAK;EACLC,OAAO;EACPC,QAAQ;EACRC,MAAM;EACNC,SAAS;EACTC,WAAW;EACXC,UAAU;EACVC,KAAK,EAAEC,cAAc;EACrBC,MAAM;EACN,GAAGC;AACE,CAAC,KAAK;EACX,MAAM;IAAEC;EAAO,CAAC,GAAGhB,gBAAgB,CAACa,cAAc,CAAC;EACnD,MAAM;IAAEI,eAAe,GAAGD,MAAM,aAANA,MAAM,uBAANA,MAAM,CAAEE;EAAQ,CAAC,GAAGpB,UAAU,CAACqB,OAAO,CAACd,KAAK,CAAC,IAAI,CAAC,CAAC;EAE7E,oBACET,KAAA,CAAAwB,aAAA,CAACrB,IAAI,EAAAsB,QAAA;IACHhB,KAAK,EAAE,CACL;MACEiB,KAAK,EAAEnB,IAAI;MACXoB,MAAM,EAAEpB,IAAI;MACZqB,YAAY,EAAErB,IAAI,GAAG,CAAC;MACtBc;IACF,CAAC,EACDZ,KAAK;EACL,GACEU,IAAI,GAEP,OAAOX,MAAM,KAAK,UAAU,IAAIA,MAAM,CAAC;IAAED;EAAK,CAAC,CAAC,EAChD,OAAOC,MAAM,KAAK,UAAU,iBAC3BR,KAAA,CAAAwB,aAAA,CAACvB,KAAK;IACJiB,MAAM,EAAEA,MAAO;IACfV,MAAM,EAAEA,MAAO;IACfC,KAAK,EAAE;MAAEiB,KAAK,EAAEnB,IAAI;MAAEoB,MAAM,EAAEpB,IAAI;MAAEqB,YAAY,EAAErB,IAAI,GAAG;IAAE,CAAE;IAC7DG,OAAO,EAAEA,OAAQ;IACjBC,QAAQ,EAAEA,QAAS;IACnBC,MAAM,EAAEA,MAAO;IACfC,SAAS,EAAEA,SAAU;IACrBC,WAAW,EAAEA,WAAY;IACzBC,UAAU,EAAEA,UAAW;IACvBc,gCAAgC;EAAA,CACjC,CAEC,CAAC;AAEX,CAAC;AAEDvB,WAAW,CAACwB,WAAW,GAAG,cAAc;AAExC,eAAexB,WAAW", "ignoreList": []}