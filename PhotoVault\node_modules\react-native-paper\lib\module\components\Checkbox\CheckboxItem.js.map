{"version": 3, "names": ["React", "StyleSheet", "View", "Checkbox", "CheckboxAndroid", "CheckboxIOS", "useInternalTheme", "TouchableRipple", "Text", "CheckboxItem", "style", "status", "label", "onPress", "onLongPress", "labelStyle", "theme", "themeOverrides", "testID", "mode", "position", "accessibilityLabel", "disabled", "labelVariant", "labelMaxFontSizeMultiplier", "rippleColor", "background", "hitSlop", "props", "checkboxProps", "isLeading", "checkbox", "createElement", "textColor", "isV3", "colors", "onSurface", "text", "disabledTextColor", "onSurfaceDisabled", "textAlign", "computedStyle", "color", "accessibilityRole", "accessibilityState", "checked", "styles", "container", "pointerEvents", "importantForAccessibility", "variant", "maxFontSizeMultiplier", "font", "displayName", "create", "flexDirection", "alignItems", "justifyContent", "paddingVertical", "paddingHorizontal", "flexShrink", "flexGrow", "fontSize"], "sourceRoot": "../../../../src", "sources": ["components/Checkbox/CheckboxItem.tsx"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAKEC,UAAU,EAEVC,IAAI,QAEC,cAAc;AAErB,OAAOC,QAAQ,MAAM,YAAY;AACjC,OAAOC,eAAe,MAAM,mBAAmB;AAC/C,OAAOC,WAAW,MAAM,eAAe;AACvC,SAASC,gBAAgB,QAAQ,oBAAoB;AAErD,OAAOC,eAAe,MAEf,oCAAoC;AAC3C,OAAOC,IAAI,MAAM,oBAAoB;AAgGrC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA,MAAMC,YAAY,GAAGA,CAAC;EACpBC,KAAK;EACLC,MAAM;EACNC,KAAK;EACLC,OAAO;EACPC,WAAW;EACXC,UAAU;EACVC,KAAK,EAAEC,cAAc;EACrBC,MAAM;EACNC,IAAI;EACJC,QAAQ,GAAG,UAAU;EACrBC,kBAAkB,GAAGT,KAAK;EAC1BU,QAAQ;EACRC,YAAY,GAAG,WAAW;EAC1BC,0BAA0B,GAAG,GAAG;EAChCC,WAAW;EACXC,UAAU;EACVC,OAAO;EACP,GAAGC;AACE,CAAC,KAAK;EACX,MAAMZ,KAAK,GAAGV,gBAAgB,CAACW,cAAc,CAAC;EAC9C,MAAMY,aAAa,GAAG;IAAE,GAAGD,KAAK;IAAEjB,MAAM;IAAEK,KAAK;IAAEM;EAAS,CAAC;EAC3D,MAAMQ,SAAS,GAAGV,QAAQ,KAAK,SAAS;EACxC,IAAIW,QAAQ;EAEZ,IAAIZ,IAAI,KAAK,SAAS,EAAE;IACtBY,QAAQ,gBAAG/B,KAAA,CAAAgC,aAAA,CAAC5B,eAAe,EAAKyB,aAAgB,CAAC;EACnD,CAAC,MAAM,IAAIV,IAAI,KAAK,KAAK,EAAE;IACzBY,QAAQ,gBAAG/B,KAAA,CAAAgC,aAAA,CAAC3B,WAAW,EAAKwB,aAAgB,CAAC;EAC/C,CAAC,MAAM;IACLE,QAAQ,gBAAG/B,KAAA,CAAAgC,aAAA,CAAC7B,QAAQ,EAAK0B,aAAgB,CAAC;EAC5C;EAEA,MAAMI,SAAS,GAAGjB,KAAK,CAACkB,IAAI,GAAGlB,KAAK,CAACmB,MAAM,CAACC,SAAS,GAAGpB,KAAK,CAACmB,MAAM,CAACE,IAAI;EACzE,MAAMC,iBAAiB,GAAGtB,KAAK,CAACkB,IAAI,GAChClB,KAAK,CAACmB,MAAM,CAACI,iBAAiB,GAC9BvB,KAAK,CAACmB,MAAM,CAACb,QAAQ;EACzB,MAAMkB,SAAS,GAAGV,SAAS,GAAG,OAAO,GAAG,MAAM;EAE9C,MAAMW,aAAa,GAAG;IACpBC,KAAK,EAAEpB,QAAQ,GAAGgB,iBAAiB,GAAGL,SAAS;IAC/CO;EACF,CAAc;EAEd,oBACExC,KAAA,CAAAgC,aAAA,CAACzB,eAAe;IACdc,kBAAkB,EAAEA,kBAAmB;IACvCsB,iBAAiB,EAAC,UAAU;IAC5BC,kBAAkB,EAAE;MAClBC,OAAO,EAAElC,MAAM,KAAK,SAAS;MAC7BW;IACF,CAAE;IACFT,OAAO,EAAEA,OAAQ;IACjBC,WAAW,EAAEA,WAAY;IACzBI,MAAM,EAAEA,MAAO;IACfI,QAAQ,EAAEA,QAAS;IACnBG,WAAW,EAAEA,WAAY;IACzBT,KAAK,EAAEA,KAAM;IACbU,UAAU,EAAEA,UAAW;IACvBC,OAAO,EAAEA;EAAQ,gBAEjB3B,KAAA,CAAAgC,aAAA,CAAC9B,IAAI;IACHQ,KAAK,EAAE,CAACoC,MAAM,CAACC,SAAS,EAAErC,KAAK,CAAE;IACjCsC,aAAa,EAAC,MAAM;IACpBC,yBAAyB,EAAC;EAAqB,GAE9CnB,SAAS,IAAIC,QAAQ,eACtB/B,KAAA,CAAAgC,aAAA,CAACxB,IAAI;IACH0C,OAAO,EAAE3B,YAAa;IACtBL,MAAM,EAAE,GAAGA,MAAM,OAAQ;IACzBiC,qBAAqB,EAAE3B,0BAA2B;IAClDd,KAAK,EAAE,CACLoC,MAAM,CAAClC,KAAK,EACZ,CAACI,KAAK,CAACkB,IAAI,IAAIY,MAAM,CAACM,IAAI,EAC1BX,aAAa,EACb1B,UAAU;EACV,GAEDH,KACG,CAAC,EACN,CAACkB,SAAS,IAAIC,QACX,CACS,CAAC;AAEtB,CAAC;AAEDtB,YAAY,CAAC4C,WAAW,GAAG,eAAe;AAE1C,eAAe5C,YAAY;;AAE3B;AACA,SAASA,YAAY;AAErB,MAAMqC,MAAM,GAAG7C,UAAU,CAACqD,MAAM,CAAC;EAC/BP,SAAS,EAAE;IACTQ,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,eAAe;IAC/BC,eAAe,EAAE,CAAC;IAClBC,iBAAiB,EAAE;EACrB,CAAC;EACD/C,KAAK,EAAE;IACLgD,UAAU,EAAE,CAAC;IACbC,QAAQ,EAAE;EACZ,CAAC;EACDT,IAAI,EAAE;IACJU,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}