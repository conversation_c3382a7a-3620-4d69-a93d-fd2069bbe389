# 🔐 LocalOne Vault - Secure Offline Photo Storage

A 100% offline mobile app built with Expo + React Native that allows users to securely store and view photos on their device without any backend or login requirements.

## 🔐 Features

### PIN Lock Screen

- **First Launch**: Create a secure 4-6 digit PIN
- **Future Launches**: Enter PIN to unlock the vault
- **Secure Storage**: PIN stored using react-native-mmkv for maximum security
- **Attempt Limiting**: Protection against brute force attacks

### Offline Image Vault

- **Gallery View**: Scrollable grid layout displaying all saved images
- **Add Photos**: Import from device gallery or take new photos
- **Secure Import**: Imports photos with security reminders to manually delete originals
- **Manual Security**: Guided process to ensure photos only exist in the vault
- **Private Storage**: All images saved in app's private directory (not in camera roll)
- **Image Management**: View details and delete images with long press

### Camera Integration

- **Take Photos**: Built-in camera with front/back toggle
- **Instant Save**: Photos automatically saved to secure vault
- **Permission Handling**: Graceful camera permission requests

## 🛠️ Tech Stack

- **Framework**: Expo (React Native)
- **Language**: TypeScript
- **UI Library**: react-native-paper (Material Design 3)
- **Secure Storage**: react-native-mmkv
- **Camera**: expo-camera
- **Media Access**: expo-image-picker
- **File System**: expo-file-system
- **Navigation**: @react-navigation/native + @react-navigation/stack

## 📦 Installation & Setup

1. **Clone and Install**:

   ```bash
   cd LocalOneVault
   npm install
   ```

2. **Start Development Server**:

   ```bash
   npm start
   ```

3. **Run on Device**:
   - **Web**: Press `w` or visit http://localhost:8081
   - **Android**: Press `a` or scan QR code with Expo Go
   - **iOS**: Press `i` or scan QR code with Camera app

## 🏗️ Project Structure

```
src/
├── components/          # Reusable UI components
│   ├── PinInput.tsx    # PIN input with numpad
│   └── GalleryItem.tsx # Gallery grid item
├── screens/            # App screens
│   ├── PinSetupScreen.tsx   # Initial PIN creation
│   ├── PinLoginScreen.tsx   # PIN verification
│   ├── GalleryScreen.tsx    # Main photo gallery
│   └── CameraScreen.tsx     # Camera interface
├── services/           # Business logic
│   ├── StorageService.ts    # MMKV + file system operations
│   └── MediaService.ts      # Media library integration
├── navigation/         # Navigation setup
│   └── AppNavigator.tsx     # Stack navigator
├── types/             # TypeScript definitions
│   └── index.ts       # App-wide types
└── utils/             # Helper functions
    └── index.ts       # Utility functions
```

## 🔒 Security Features

- **Local-Only Storage**: No data ever leaves the device
- **Encrypted PIN Storage**: Using react-native-mmkv secure storage
- **Private File System**: Images stored in app's private directory
- **No Network Access**: 100% offline functionality
- **Gesture Protection**: Swipe gestures disabled on sensitive screens

## 📱 Usage

1. **First Launch**: Set up your 4-6 digit PIN
2. **Add Photos**: Use the floating action button to:
   - Take a new photo with the camera
   - Import existing photos from your gallery
3. **View Photos**: Tap any photo to view it full-screen
4. **Delete Photos**: Long press any photo to delete it
5. **Security**: App locks automatically and requires PIN on restart

## 🎨 UI/UX Features

- **Material Design 3**: Modern, clean interface
- **Loading States**: Smooth user experience with proper loading indicators
- **Error Handling**: Graceful error messages and recovery
- **Responsive Design**: Works on various screen sizes
- **Accessibility**: Proper labels and navigation

## 🚀 Future Enhancements

- Biometric authentication (fingerprint/face unlock)
- Photo organization with albums/tags
- Search functionality
- Backup/restore options
- Photo editing capabilities
- Slideshow mode

## 📄 License

This project is for educational purposes. Feel free to use and modify as needed.

---

**Built with ❤️ using Expo + React Native**
