{"version": 3, "names": ["color", "getActiveTintColor", "activeColor", "defaultColor", "theme", "isV3", "colors", "onSecondaryContainer", "getInactiveTintColor", "inactiveColor", "onSurfaceVariant", "alpha", "rgb", "string", "getLabelColor", "tintColor", "hasColor", "focused", "onSurface"], "sourceRoot": "../../../../src", "sources": ["components/BottomNavigation/utils.ts"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AAUzB,OAAO,MAAMC,kBAAkB,GAAGA,CAAC;EACjCC,WAAW;EACXC,YAAY;EACZC;AAGF,CAAC,KAAK;EACJ,IAAI,OAAOF,WAAW,KAAK,QAAQ,EAAE;IACnC,OAAOA,WAAW;EACpB;EAEA,IAAIE,KAAK,CAACC,IAAI,EAAE;IACd,OAAOD,KAAK,CAACE,MAAM,CAACC,oBAAoB;EAC1C;EAEA,OAAOJ,YAAY;AACrB,CAAC;AAED,OAAO,MAAMK,oBAAoB,GAAGA,CAAC;EACnCC,aAAa;EACbN,YAAY;EACZC;AAGF,CAAC,KAAK;EACJ,IAAI,OAAOK,aAAa,KAAK,QAAQ,EAAE;IACrC,OAAOA,aAAa;EACtB;EAEA,IAAIL,KAAK,CAACC,IAAI,EAAE;IACd,OAAOD,KAAK,CAACE,MAAM,CAACI,gBAAgB;EACtC;EAEA,OAAOV,KAAK,CAACG,YAAY,CAAC,CAACQ,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AACtD,CAAC;AAED,OAAO,MAAMC,aAAa,GAAGA,CAAC;EAC5BC,SAAS;EACTC,QAAQ;EACRC,OAAO;EACPd,YAAY;EACZC;AAKF,CAAC,KAAK;EACJ,IAAIY,QAAQ,EAAE;IACZ,OAAOD,SAAS;EAClB;EAEA,IAAIX,KAAK,CAACC,IAAI,EAAE;IACd,IAAIY,OAAO,EAAE;MACX,OAAOb,KAAK,CAACE,MAAM,CAACY,SAAS;IAC/B;IACA,OAAOd,KAAK,CAACE,MAAM,CAACI,gBAAgB;EACtC;EAEA,OAAOP,YAAY;AACrB,CAAC", "ignoreList": []}