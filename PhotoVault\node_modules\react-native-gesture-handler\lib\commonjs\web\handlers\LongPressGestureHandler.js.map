{"version": 3, "names": ["_State", "require", "_Gesture<PERSON><PERSON>ler", "_interopRequireDefault", "e", "__esModule", "default", "DEFAULT_MIN_DURATION_MS", "DEFAULT_MAX_DIST_DP", "SCALING_FACTOR", "LongPressGestureHandler", "Gesture<PERSON>andler", "minDurationMs", "defaultMaxDistSq", "maxDistSq", "numberOfPointers", "startX", "startY", "startTime", "previousTime", "init", "ref", "propsRef", "config", "enableContextMenu", "undefined", "transformNativeEvent", "duration", "Date", "now", "updateGestureConfig", "enabled", "props", "maxDist", "resetConfig", "onStateChange", "_newState", "_oldState", "clearTimeout", "activationTimeout", "onPointerDown", "event", "isButtonInConfig", "button", "tracker", "addToTracker", "x", "y", "tryBegin", "tryActivate", "tryToSendTouchEvent", "onPointerAdd", "trackedPointersCount", "fail", "absoluteCoordsAverage", "getAbsoluteCoordsAverage", "onPointerMove", "track", "checkDistanceFail", "onPointerOutOfBounds", "onPointerUp", "removeFromTracker", "pointerId", "state", "State", "ACTIVE", "end", "onPointerRemove", "UNDETERMINED", "begin", "setTimeout", "activate", "dx", "dy", "distSq", "cancel", "exports"], "sourceRoot": "../../../../src", "sources": ["web/handlers/LongPressGestureHandler.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AAGA,IAAAC,eAAA,GAAAC,sBAAA,CAAAF,OAAA;AAA8C,SAAAE,uBAAAC,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAE9C,MAAMG,uBAAuB,GAAG,GAAG;AACnC,MAAMC,mBAAmB,GAAG,EAAE;AAC9B,MAAMC,cAAc,GAAG,EAAE;AAEV,MAAMC,uBAAuB,SAASC,uBAAc,CAAC;EAC1DC,aAAa,GAAGL,uBAAuB;EACvCM,gBAAgB,GAAGL,mBAAmB,GAAGC,cAAc;EAEvDK,SAAS,GAAG,IAAI,CAACD,gBAAgB;EACjCE,gBAAgB,GAAG,CAAC;EACpBC,MAAM,GAAG,CAAC;EACVC,MAAM,GAAG,CAAC;EAEVC,SAAS,GAAG,CAAC;EACbC,YAAY,GAAG,CAAC;EAIjBC,IAAIA,CAACC,GAAW,EAAEC,QAAkC,EAAE;IAC3D,IAAI,IAAI,CAACC,MAAM,CAACC,iBAAiB,KAAKC,SAAS,EAAE;MAC/C,IAAI,CAACF,MAAM,CAACC,iBAAiB,GAAG,KAAK;IACvC;IAEA,KAAK,CAACJ,IAAI,CAACC,GAAG,EAAEC,QAAQ,CAAC;EAC3B;EAEUI,oBAAoBA,CAAA,EAAG;IAC/B,OAAO;MACL,GAAG,KAAK,CAACA,oBAAoB,CAAC,CAAC;MAC/BC,QAAQ,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,IAAI,CAACX;IAC9B,CAAC;EACH;EAEOY,mBAAmBA,CAAC;IAAEC,OAAO,GAAG,IAAI;IAAE,GAAGC;EAAc,CAAC,EAAQ;IACrE,KAAK,CAACF,mBAAmB,CAAC;MAAEC,OAAO,EAAEA,OAAO;MAAE,GAAGC;IAAM,CAAC,CAAC;IAEzD,IAAI,IAAI,CAACT,MAAM,CAACX,aAAa,KAAKa,SAAS,EAAE;MAC3C,IAAI,CAACb,aAAa,GAAG,IAAI,CAACW,MAAM,CAACX,aAAa;IAChD;IAEA,IAAI,IAAI,CAACW,MAAM,CAACU,OAAO,KAAKR,SAAS,EAAE;MACrC,IAAI,CAACX,SAAS,GAAG,IAAI,CAACS,MAAM,CAACU,OAAO,GAAG,IAAI,CAACV,MAAM,CAACU,OAAO;IAC5D;IAEA,IAAI,IAAI,CAACV,MAAM,CAACR,gBAAgB,KAAKU,SAAS,EAAE;MAC9C,IAAI,CAACV,gBAAgB,GAAG,IAAI,CAACQ,MAAM,CAACR,gBAAgB;IACtD;EACF;EAEUmB,WAAWA,CAAA,EAAS;IAC5B,KAAK,CAACA,WAAW,CAAC,CAAC;IACnB,IAAI,CAACtB,aAAa,GAAGL,uBAAuB;IAC5C,IAAI,CAACO,SAAS,GAAG,IAAI,CAACD,gBAAgB;EACxC;EAEUsB,aAAaA,CAACC,SAAgB,EAAEC,SAAgB,EAAQ;IAChEC,YAAY,CAAC,IAAI,CAACC,iBAAiB,CAAC;EACtC;EAEUC,aAAaA,CAACC,KAAmB,EAAQ;IACjD,IAAI,CAAC,IAAI,CAACC,gBAAgB,CAACD,KAAK,CAACE,MAAM,CAAC,EAAE;MACxC;IACF;IAEA,IAAI,CAACC,OAAO,CAACC,YAAY,CAACJ,KAAK,CAAC;IAChC,KAAK,CAACD,aAAa,CAACC,KAAK,CAAC;IAE1B,IAAI,CAACzB,MAAM,GAAGyB,KAAK,CAACK,CAAC;IACrB,IAAI,CAAC7B,MAAM,GAAGwB,KAAK,CAACM,CAAC;IAErB,IAAI,CAACC,QAAQ,CAAC,CAAC;IACf,IAAI,CAACC,WAAW,CAAC,CAAC;IAElB,IAAI,CAACC,mBAAmB,CAACT,KAAK,CAAC;EACjC;EACUU,YAAYA,CAACV,KAAmB,EAAQ;IAChD,KAAK,CAACU,YAAY,CAACV,KAAK,CAAC;IACzB,IAAI,CAACG,OAAO,CAACC,YAAY,CAACJ,KAAK,CAAC;IAEhC,IAAI,IAAI,CAACG,OAAO,CAACQ,oBAAoB,GAAG,IAAI,CAACrC,gBAAgB,EAAE;MAC7D,IAAI,CAACsC,IAAI,CAAC,CAAC;MACX;IACF;IAEA,MAAMC,qBAAqB,GAAG,IAAI,CAACV,OAAO,CAACW,wBAAwB,CAAC,CAAC;IAErE,IAAI,CAACvC,MAAM,GAAGsC,qBAAqB,CAACR,CAAC;IACrC,IAAI,CAAC7B,MAAM,GAAGqC,qBAAqB,CAACP,CAAC;IAErC,IAAI,CAACE,WAAW,CAAC,CAAC;EACpB;EAEUO,aAAaA,CAACf,KAAmB,EAAQ;IACjD,KAAK,CAACe,aAAa,CAACf,KAAK,CAAC;IAC1B,IAAI,CAACG,OAAO,CAACa,KAAK,CAAChB,KAAK,CAAC;IACzB,IAAI,CAACiB,iBAAiB,CAAC,CAAC;EAC1B;EAEUC,oBAAoBA,CAAClB,KAAmB,EAAQ;IACxD,KAAK,CAACkB,oBAAoB,CAAClB,KAAK,CAAC;IACjC,IAAI,CAACG,OAAO,CAACa,KAAK,CAAChB,KAAK,CAAC;IACzB,IAAI,CAACiB,iBAAiB,CAAC,CAAC;EAC1B;EAEUE,WAAWA,CAACnB,KAAmB,EAAQ;IAC/C,KAAK,CAACmB,WAAW,CAACnB,KAAK,CAAC;IACxB,IAAI,CAACG,OAAO,CAACiB,iBAAiB,CAACpB,KAAK,CAACqB,SAAS,CAAC;IAE/C,IAAI,IAAI,CAACC,KAAK,KAAKC,YAAK,CAACC,MAAM,EAAE;MAC/B,IAAI,CAACC,GAAG,CAAC,CAAC;IACZ,CAAC,MAAM;MACL,IAAI,CAACb,IAAI,CAAC,CAAC;IACb;EACF;EAEUc,eAAeA,CAAC1B,KAAmB,EAAQ;IACnD,KAAK,CAAC0B,eAAe,CAAC1B,KAAK,CAAC;IAC5B,IAAI,CAACG,OAAO,CAACiB,iBAAiB,CAACpB,KAAK,CAACqB,SAAS,CAAC;IAE/C,IACE,IAAI,CAAClB,OAAO,CAACQ,oBAAoB,GAAG,IAAI,CAACrC,gBAAgB,IACzD,IAAI,CAACgD,KAAK,KAAKC,YAAK,CAACC,MAAM,EAC3B;MACA,IAAI,CAACZ,IAAI,CAAC,CAAC;IACb;EACF;EAEQL,QAAQA,CAAA,EAAS;IACvB,IAAI,IAAI,CAACe,KAAK,KAAKC,YAAK,CAACI,YAAY,EAAE;MACrC;IACF;IAEA,IAAI,CAACjD,YAAY,GAAGS,IAAI,CAACC,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACX,SAAS,GAAG,IAAI,CAACC,YAAY;IAElC,IAAI,CAACkD,KAAK,CAAC,CAAC;EACd;EAEQpB,WAAWA,CAAA,EAAS;IAC1B,IAAI,IAAI,CAACL,OAAO,CAACQ,oBAAoB,KAAK,IAAI,CAACrC,gBAAgB,EAAE;MAC/D;IACF;IAEA,IAAI,IAAI,CAACH,aAAa,GAAG,CAAC,EAAE;MAC1B,IAAI,CAAC2B,iBAAiB,GAAG+B,UAAU,CAAC,MAAM;QACxC,IAAI,CAACC,QAAQ,CAAC,CAAC;MACjB,CAAC,EAAE,IAAI,CAAC3D,aAAa,CAAC;IACxB,CAAC,MAAM,IAAI,IAAI,CAACA,aAAa,KAAK,CAAC,EAAE;MACnC,IAAI,CAAC2D,QAAQ,CAAC,CAAC;IACjB;EACF;EAEQb,iBAAiBA,CAAA,EAAS;IAChC,MAAMJ,qBAAqB,GAAG,IAAI,CAACV,OAAO,CAACW,wBAAwB,CAAC,CAAC;IAErE,MAAMiB,EAAE,GAAGlB,qBAAqB,CAACR,CAAC,GAAG,IAAI,CAAC9B,MAAM;IAChD,MAAMyD,EAAE,GAAGnB,qBAAqB,CAACP,CAAC,GAAG,IAAI,CAAC9B,MAAM;IAChD,MAAMyD,MAAM,GAAGF,EAAE,GAAGA,EAAE,GAAGC,EAAE,GAAGA,EAAE;IAEhC,IAAIC,MAAM,IAAI,IAAI,CAAC5D,SAAS,EAAE;MAC5B;IACF;IAEA,IAAI,IAAI,CAACiD,KAAK,KAAKC,YAAK,CAACC,MAAM,EAAE;MAC/B,IAAI,CAACU,MAAM,CAAC,CAAC;IACf,CAAC,MAAM;MACL,IAAI,CAACtB,IAAI,CAAC,CAAC;IACb;EACF;AACF;AAACuB,OAAA,CAAAtE,OAAA,GAAAI,uBAAA", "ignoreList": []}