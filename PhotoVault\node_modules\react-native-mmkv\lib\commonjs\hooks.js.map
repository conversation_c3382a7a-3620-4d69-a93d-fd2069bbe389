{"version": 3, "names": ["_react", "require", "_MMKV", "isConfigurationEqual", "left", "right", "<PERSON><PERSON><PERSON>", "id", "path", "mode", "defaultInstance", "getDefaultInstance", "MMKV", "useMMKV", "configuration", "instance", "useRef", "lastConfiguration", "current", "createMMKVHook", "getter", "key", "mmkv", "bump", "setBump", "useState", "value", "useMemo", "set", "useCallback", "v", "newValue", "delete", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Error", "useEffect", "listener", "addOnValueChangedListener", "<PERSON><PERSON><PERSON>", "b", "remove", "useMMKVString", "exports", "getString", "useMMKVNumber", "getNumber", "useMMKVBoolean", "getBoolean", "useMMKVBuffer", "<PERSON><PERSON><PERSON><PERSON>", "useMMKVObject", "json", "<PERSON><PERSON><PERSON>", "undefined", "JSON", "parse", "setValue", "Function", "<PERSON><PERSON><PERSON>", "currentValue", "stringify", "useMMKVListener", "valueChangedListener", "ref"], "sourceRoot": "../../src", "sources": ["hooks.ts"], "mappings": ";;;;;;;;;;;AAAA,IAAAA,MAAA,GAAAC,OAAA;AACA,IAAAC,KAAA,GAAAD,OAAA;AAGA,SAASE,oBAAoBA,CAC3BC,IAAoB,EACpBC,KAAqB,EACZ;EACT,IAAID,IAAI,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,EAAE,OAAOD,IAAI,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI;EAEvE,OACED,IAAI,CAACE,aAAa,KAAKD,KAAK,CAACC,aAAa,IAC1CF,IAAI,CAACG,EAAE,KAAKF,KAAK,CAACE,EAAE,IACpBH,IAAI,CAACI,IAAI,KAAKH,KAAK,CAACG,IAAI,IACxBJ,IAAI,CAACK,IAAI,KAAKJ,KAAK,CAACI,IAAI;AAE5B;AAEA,IAAIC,eAA4B,GAAG,IAAI;AACvC,SAASC,kBAAkBA,CAAA,EAAS;EAClC,IAAID,eAAe,IAAI,IAAI,EAAE;IAC3BA,eAAe,GAAG,IAAIE,UAAI,CAAC,CAAC;EAC9B;EACA,OAAOF,eAAe;AACxB;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEO,SAASG,OAAOA,CAACC,aAA6B,EAAQ;EAC3D,MAAMC,QAAQ,GAAG,IAAAC,aAAM,EAAO,CAAC;EAC/B,MAAMC,iBAAiB,GAAG,IAAAD,aAAM,EAAgB,CAAC;EAEjD,IAAIF,aAAa,IAAI,IAAI,EAAE,OAAOH,kBAAkB,CAAC,CAAC;EAEtD,IACEI,QAAQ,CAACG,OAAO,IAAI,IAAI,IACxB,CAACf,oBAAoB,CAACc,iBAAiB,CAACC,OAAO,EAAEJ,aAAa,CAAC,EAC/D;IACAG,iBAAiB,CAACC,OAAO,GAAGJ,aAAa;IACzCC,QAAQ,CAACG,OAAO,GAAG,IAAIN,UAAI,CAACE,aAAa,CAAC;EAC5C;EAEA,OAAOC,QAAQ,CAACG,OAAO;AACzB;AAEA,SAASC,cAAcA,CAIrBC,MAA0C,EAAE;EAC5C,OAAO,CACLC,GAAW,EACXN,QAAe,KACuC;IACtD,MAAMO,IAAI,GAAGP,QAAQ,IAAIJ,kBAAkB,CAAC,CAAC;IAE7C,MAAM,CAACY,IAAI,EAAEC,OAAO,CAAC,GAAG,IAAAC,eAAQ,EAAC,CAAC,CAAC;IACnC,MAAMC,KAAK,GAAG,IAAAC,cAAO,EAAC,MAAM;MAC1B;MACA;MACA;MACAJ,IAAI;MACJ,OAAOH,MAAM,CAACE,IAAI,EAAED,GAAG,CAAC;IAC1B,CAAC,EAAE,CAACC,IAAI,EAAED,GAAG,EAAEE,IAAI,CAAC,CAAC;;IAErB;IACA,MAAMK,GAAG,GAAG,IAAAC,kBAAW,EACpBC,CAAa,IAAK;MACjB,MAAMC,QAAQ,GAAG,OAAOD,CAAC,KAAK,UAAU,GAAGA,CAAC,CAACV,MAAM,CAACE,IAAI,EAAED,GAAG,CAAC,CAAC,GAAGS,CAAC;MACnE,QAAQ,OAAOC,QAAQ;QACrB,KAAK,QAAQ;QACb,KAAK,QAAQ;QACb,KAAK,SAAS;UACZT,IAAI,CAACM,GAAG,CAACP,GAAG,EAAEU,QAAQ,CAAC;UACvB;QACF,KAAK,WAAW;UACdT,IAAI,CAACU,MAAM,CAACX,GAAG,CAAC;UAChB;QACF,KAAK,QAAQ;UACX,IAAIU,QAAQ,YAAYE,WAAW,EAAE;YACnCX,IAAI,CAACM,GAAG,CAACP,GAAG,EAAEU,QAAQ,CAAC;YACvB;UACF,CAAC,MAAM;YACL,MAAM,IAAIG,KAAK,CACb,sBAAsBH,QAAQ,qBAChC,CAAC;UACH;QACF;UACE,MAAM,IAAIG,KAAK,CAAC,cAAc,OAAOH,QAAQ,oBAAoB,CAAC;MACtE;IACF,CAAC,EACD,CAACV,GAAG,EAAEC,IAAI,CACZ,CAAC;;IAED;IACA,IAAAa,gBAAS,EAAC,MAAM;MACd,MAAMC,QAAQ,GAAGd,IAAI,CAACe,yBAAyB,CAAEC,UAAU,IAAK;QAC9D,IAAIA,UAAU,KAAKjB,GAAG,EAAE;UACtBG,OAAO,CAAEe,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;QACvB;MACF,CAAC,CAAC;MACF,OAAO,MAAMH,QAAQ,CAACI,MAAM,CAAC,CAAC;IAChC,CAAC,EAAE,CAACnB,GAAG,EAAEC,IAAI,CAAC,CAAC;IAEf,OAAO,CAACI,KAAK,EAAEE,GAAG,CAAC;EACrB,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMa,aAAa,GAAAC,OAAA,CAAAD,aAAA,GAAGtB,cAAc,CAAC,CAACJ,QAAQ,EAAEM,GAAG,KACxDN,QAAQ,CAAC4B,SAAS,CAACtB,GAAG,CACxB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMuB,aAAa,GAAAF,OAAA,CAAAE,aAAA,GAAGzB,cAAc,CAAC,CAACJ,QAAQ,EAAEM,GAAG,KACxDN,QAAQ,CAAC8B,SAAS,CAACxB,GAAG,CACxB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAMyB,cAAc,GAAAJ,OAAA,CAAAI,cAAA,GAAG3B,cAAc,CAAC,CAACJ,QAAQ,EAAEM,GAAG,KACzDN,QAAQ,CAACgC,UAAU,CAAC1B,GAAG,CACzB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,MAAM2B,aAAa,GAAAN,OAAA,CAAAM,aAAA,GAAG7B,cAAc,CAAC,CAACJ,QAAQ,EAAEM,GAAG,KACxDN,QAAQ,CAACkC,SAAS,CAAC5B,GAAG,CACxB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAAS6B,aAAaA,CAC3B7B,GAAW,EACXN,QAAe,EAMf;EACA,MAAM,CAACoC,IAAI,EAAEC,OAAO,CAAC,GAAGX,aAAa,CAACpB,GAAG,EAAEN,QAAQ,CAAC;EAEpD,MAAMW,KAAK,GAAG,IAAAC,cAAO,EAAC,MAAM;IAC1B,IAAIwB,IAAI,IAAI,IAAI,EAAE,OAAOE,SAAS;IAClC,OAAOC,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC;EACzB,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EAEV,MAAMK,QAAQ,GAAG,IAAA3B,kBAAW,EACzBC,CAA6D,IAAK;IACjE,IAAIA,CAAC,YAAY2B,QAAQ,EAAE;MACzBL,OAAO,CAAEM,WAAW,IAAK;QACvB,MAAMC,YAAY,GAChBD,WAAW,IAAI,IAAI,GAAIJ,IAAI,CAACC,KAAK,CAACG,WAAW,CAAC,GAASL,SAAS;QAClE,MAAMtB,QAAQ,GAAGD,CAAC,CAAC6B,YAAY,CAAC;QAChC;QACA,OAAO5B,QAAQ,IAAI,IAAI,GAAGuB,IAAI,CAACM,SAAS,CAAC7B,QAAQ,CAAC,GAAGsB,SAAS;MAChE,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,MAAMtB,QAAQ,GAAGD,CAAC,IAAI,IAAI,GAAGwB,IAAI,CAACM,SAAS,CAAC9B,CAAC,CAAC,GAAGuB,SAAS;MAC1DD,OAAO,CAACrB,QAAQ,CAAC;IACnB;EACF,CAAC,EACD,CAACqB,OAAO,CACV,CAAC;EAED,OAAO,CAAC1B,KAAK,EAAE8B,QAAQ,CAAC;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACO,SAASK,eAAeA,CAC7BC,oBAA2C,EAC3C/C,QAAe,EACT;EACN,MAAMgD,GAAG,GAAG,IAAA/C,aAAM,EAAC8C,oBAAoB,CAAC;EACxCC,GAAG,CAAC7C,OAAO,GAAG4C,oBAAoB;EAElC,MAAMxC,IAAI,GAAGP,QAAQ,IAAIJ,kBAAkB,CAAC,CAAC;EAE7C,IAAAwB,gBAAS,EAAC,MAAM;IACd,MAAMC,QAAQ,GAAGd,IAAI,CAACe,yBAAyB,CAAEC,UAAU,IAAK;MAC9DyB,GAAG,CAAC7C,OAAO,CAACoB,UAAU,CAAC;IACzB,CAAC,CAAC;IACF,OAAO,MAAMF,QAAQ,CAACI,MAAM,CAAC,CAAC;EAChC,CAAC,EAAE,CAAClB,IAAI,CAAC,CAAC;AACZ", "ignoreList": []}