{"version": 3, "names": ["_reactNative", "require", "_ModuleNotFoundError", "mmkvPlatformModule", "getMMKVPlatformContextTurboModule", "TurboModuleRegistry", "getEnforcing", "e", "ModuleNotFoundError"], "sourceRoot": "../../src", "sources": ["NativeMmkvPlatformContext.ts"], "mappings": ";;;;;;AACA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,oBAAA,GAAAD,OAAA;AAoBA,IAAIE,kBAA+B;AAE5B,SAASC,iCAAiCA,CAAA,EAAS;EACxD,IAAI;IACF,IAAID,kBAAkB,IAAI,IAAI,EAAE;MAC9B;MACAA,kBAAkB,GAAGE,gCAAmB,CAACC,YAAY,CACnD,qBACF,CAAC;IACH;IACA,OAAOH,kBAAkB;EAC3B,CAAC,CAAC,OAAOI,CAAC,EAAE;IACV;IACA,MAAM,IAAIC,wCAAmB,CAACD,CAAC,CAAC;EAClC;AACF", "ignoreList": []}