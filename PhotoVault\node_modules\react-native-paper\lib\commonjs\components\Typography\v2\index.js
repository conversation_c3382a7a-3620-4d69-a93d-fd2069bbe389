"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
Object.defineProperty(exports, "Caption", {
  enumerable: true,
  get: function () {
    return _Caption.default;
  }
});
Object.defineProperty(exports, "Headline", {
  enumerable: true,
  get: function () {
    return _Headline.default;
  }
});
Object.defineProperty(exports, "Paragraph", {
  enumerable: true,
  get: function () {
    return _Paragraph.default;
  }
});
Object.defineProperty(exports, "Subheading", {
  enumerable: true,
  get: function () {
    return _Subheading.default;
  }
});
Object.defineProperty(exports, "Title", {
  enumerable: true,
  get: function () {
    return _Title.default;
  }
});
var _Caption = _interopRequireDefault(require("./Caption"));
var _Headline = _interopRequireDefault(require("./Headline"));
var _Paragraph = _interopRequireDefault(require("./Paragraph"));
var _Subheading = _interopRequireDefault(require("./Subheading"));
var _Title = _interopRequireDefault(require("./Title"));
function _interopRequireDefault(e) { return e && e.__esModule ? e : { default: e }; }
//# sourceMappingURL=index.js.map