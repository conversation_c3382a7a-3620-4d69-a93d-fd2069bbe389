{"name": "react-freeze", "version": "1.0.4", "description": "React Freeze", "license": "MIT", "repository": "software-mansion/react-freeze", "main": "dist/index.js", "module": "dist/index.modern.js", "source": "src/index.tsx", "react-native": "src/index.tsx", "engines": {"node": ">=10"}, "scripts": {"build": "microbundle-crl --no-compress --format modern,cjs", "start": "microbundle-crl watch --no-compress --format modern,cjs", "prepare": "run-s build", "test": "run-s test:unit test:lint test:types test:build", "test:build": "run-s build", "test:lint": "eslint --ext '.js,.ts,.tsx' --fix src", "test:unit": "cross-env CI=1 react-scripts test --env=jsdom", "test:types": "tsc --noEmit", "test:watch": "react-scripts test --env=jsdom"}, "keywords": ["react", "freeze"], "peerDependencies": {"react": ">=17.0.0"}, "devDependencies": {"@types/jest": "^25.1.4", "@types/node": "^12.12.38", "@types/react": "^18.0.15", "@types/react-test-renderer": "^18.0.0", "@typescript-eslint/eslint-plugin": "^5.3.0", "@typescript-eslint/parser": "^5.3.0", "babel-eslint": "^10.0.3", "cross-env": "^7.0.2", "eslint": "^6.8.0", "eslint-config-prettier": "^6.7.0", "eslint-config-standard": "^14.1.0", "eslint-config-standard-react": "^9.2.0", "eslint-plugin-import": "^2.18.2", "eslint-plugin-node": "^11.0.0", "eslint-plugin-prettier": "^3.1.1", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-react": "^7.17.0", "eslint-plugin-standard": "^4.0.1", "microbundle-crl": "^0.13.10", "npm-run-all": "^4.1.5", "prettier": "^2.0.4", "react": "^18.2.0", "react-scripts": "^3.4.1", "react-test-renderer": "^18.2.0", "typescript": "^3.7.5"}, "files": ["dist", "!dist/*.test.d.ts", "src/index.tsx"]}