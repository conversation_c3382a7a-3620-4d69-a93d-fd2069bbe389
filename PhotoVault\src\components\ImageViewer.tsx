import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  Dimensions,
  Image,
  Modal,
  TouchableOpacity,
  StatusBar,
} from 'react-native';
import { Appbar, Text, IconButton, Portal } from 'react-native-paper';
import { ImageItem } from '../types';
import { formatDate, formatFileSize } from '../utils';

const { width, height } = Dimensions.get('window');

interface ImageViewerProps {
  visible: boolean;
  image: ImageItem | null;
  onClose: () => void;
  onDelete?: (image: ImageItem) => void;
}

const ImageViewer: React.FC<ImageViewerProps> = ({
  visible,
  image,
  onClose,
  onDelete,
}) => {
  const [showDetails, setShowDetails] = useState(false);

  if (!image) return null;

  const handleDelete = () => {
    if (onDelete && image) {
      onDelete(image);
      onClose();
    }
  };

  return (
    <Portal>
      <Modal
        visible={visible}
        animationType="fade"
        statusBarTranslucent
        onRequestClose={onClose}
      >
        <View style={styles.container}>
          <StatusBar hidden />
          
          {/* Header */}
          <Appbar.Header style={styles.header}>
            <Appbar.BackAction onPress={onClose} iconColor="#fff" />
            <Appbar.Content 
              title={image.filename} 
              titleStyle={styles.headerTitle}
            />
            <Appbar.Action 
              icon="information" 
              onPress={() => setShowDetails(!showDetails)}
              iconColor="#fff"
            />
            {onDelete && (
              <Appbar.Action 
                icon="delete" 
                onPress={handleDelete}
                iconColor="#fff"
              />
            )}
          </Appbar.Header>

          {/* Image */}
          <TouchableOpacity 
            style={styles.imageContainer}
            activeOpacity={1}
            onPress={() => setShowDetails(!showDetails)}
          >
            <Image
              source={{ uri: image.uri }}
              style={styles.image}
              resizeMode="contain"
            />
          </TouchableOpacity>

          {/* Details Overlay */}
          {showDetails && (
            <View style={styles.detailsOverlay}>
              <View style={styles.detailsContainer}>
                <Text variant="titleMedium" style={styles.detailsTitle}>
                  Photo Details
                </Text>
                
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Filename:</Text>
                  <Text style={styles.detailValue}>{image.filename}</Text>
                </View>
                
                <View style={styles.detailRow}>
                  <Text style={styles.detailLabel}>Created:</Text>
                  <Text style={styles.detailValue}>
                    {formatDate(image.createdAt)}
                  </Text>
                </View>
                
                {image.size && (
                  <View style={styles.detailRow}>
                    <Text style={styles.detailLabel}>Size:</Text>
                    <Text style={styles.detailValue}>
                      {formatFileSize(image.size)}
                    </Text>
                  </View>
                )}

                <IconButton
                  icon="close"
                  size={24}
                  iconColor="#fff"
                  style={styles.closeDetailsButton}
                  onPress={() => setShowDetails(false)}
                />
              </View>
            </View>
          )}
        </View>
      </Modal>
    </Portal>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
  },
  header: {
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    elevation: 0,
  },
  headerTitle: {
    color: '#fff',
    fontSize: 16,
  },
  imageContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: width,
    height: height - 100, // Account for header
  },
  detailsOverlay: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    padding: 20,
  },
  detailsContainer: {
    position: 'relative',
  },
  detailsTitle: {
    color: '#fff',
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 10,
    alignItems: 'center',
  },
  detailLabel: {
    color: '#ccc',
    fontSize: 14,
    fontWeight: '500',
  },
  detailValue: {
    color: '#fff',
    fontSize: 14,
    flex: 1,
    textAlign: 'right',
    marginLeft: 10,
  },
  closeDetailsButton: {
    position: 'absolute',
    top: -10,
    right: -10,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
  },
});

export default ImageViewer;
