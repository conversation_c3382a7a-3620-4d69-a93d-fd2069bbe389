import React from 'react';
import { TouchableOpacity, Image, StyleSheet, Dimensions } from 'react-native';
import { Card, Text } from 'react-native-paper';
import { GalleryItemProps } from '../types';

const { width } = Dimensions.get('window');
const itemSize = (width - 60) / 3; // 3 columns with margins

const GalleryItem: React.FC<GalleryItemProps> = ({ item, onPress, onLongPress }) => {
  return (
    <TouchableOpacity
      onPress={() => onPress(item)}
      onLongPress={() => onLongPress?.(item)}
      style={styles.container}
    >
      <Card style={styles.card}>
        <Image
          source={{ uri: item.uri }}
          style={styles.image}
          resizeMode="cover"
        />
        <Card.Content style={styles.content}>
          <Text variant="bodySmall" numberOfLines={1} style={styles.filename}>
            {item.filename}
          </Text>
          <Text variant="bodySmall" style={styles.date}>
            {new Date(item.createdAt).toLocaleDateString()}
          </Text>
        </Card.Content>
      </Card>
    </TouchableOpacity>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 5,
  },
  card: {
    width: itemSize,
    elevation: 2,
  },
  image: {
    width: '100%',
    height: itemSize * 0.7,
  },
  content: {
    padding: 8,
  },
  filename: {
    fontWeight: 'bold',
    marginBottom: 2,
  },
  date: {
    color: '#666',
    fontSize: 10,
  },
});

export default GalleryItem;
