{"version": 3, "names": ["_createTextEncoder", "require", "canUseDOM", "window", "document", "createElement", "hasAccessToLocalStorage", "localStorage", "KEY_WILDCARD", "inMemoryStorage", "Map", "createMMKV", "config", "<PERSON><PERSON><PERSON>", "Error", "path", "console", "warn", "storage", "getItem", "key", "get", "setItem", "value", "set", "removeItem", "delete", "clear", "length", "size", "index", "Object", "keys", "at", "domStorage", "global", "textEncoder", "createTextEncoder", "id", "includes", "keyPrefix", "prefixedKey", "clearAll", "startsWith", "toString", "getString", "undefined", "getNumber", "Number", "getBoolean", "<PERSON><PERSON><PERSON><PERSON>", "encode", "buffer", "getAllKeys", "filter", "map", "slice", "contains", "recrypt", "isReadOnly", "trim", "exports"], "sourceRoot": "../../src", "sources": ["createMMKV.web.ts"], "mappings": ";;;;;;AAEA,IAAAA,kBAAA,GAAAC,OAAA;AAFA;;AAIA,MAAMC,SAAS,GACb,OAAOC,MAAM,KAAK,WAAW,IAAIA,MAAM,CAACC,QAAQ,EAAEC,aAAa,IAAI,IAAI;AAEzE,MAAMC,uBAAuB,GAAGA,CAAA,KAAM;EACpC,IAAI;IACF;IACAH,MAAM,CAACI,YAAY;IAEnB,OAAO,IAAI;EACb,CAAC,CAAC,MAAM;IACN,OAAO,KAAK;EACd;AACF,CAAC;AAED,MAAMC,YAAY,GAAG,IAAI;AACzB,MAAMC,eAAe,GAAG,IAAIC,GAAG,CAAiB,CAAC;AAE1C,MAAMC,UAAU,GAAIC,MAAqB,IAAiB;EAC/D,IAAIA,MAAM,CAACC,aAAa,IAAI,IAAI,EAAE;IAChC,MAAM,IAAIC,KAAK,CAAC,gDAAgD,CAAC;EACnE;EACA,IAAIF,MAAM,CAACG,IAAI,IAAI,IAAI,EAAE;IACvB,MAAM,IAAID,KAAK,CAAC,uCAAuC,CAAC;EAC1D;;EAEA;EACA,IAAI,CAACR,uBAAuB,CAAC,CAAC,IAAIJ,SAAS,EAAE;IAC3Cc,OAAO,CAACC,IAAI,CACV,6FACF,CAAC;EACH;EAEA,MAAMC,OAAO,GAAGA,CAAA,KAAM;IACpB,IAAI,CAAChB,SAAS,EAAE;MACd,MAAM,IAAIY,KAAK,CACb,kFACF,CAAC;IACH;IAEA,IAAI,CAACR,uBAAuB,CAAC,CAAC,EAAE;MAC9B,OAAO;QACLa,OAAO,EAAGC,GAAW,IAAKX,eAAe,CAACY,GAAG,CAACD,GAAG,CAAC,IAAI,IAAI;QAC1DE,OAAO,EAAEA,CAACF,GAAW,EAAEG,KAAa,KAClCd,eAAe,CAACe,GAAG,CAACJ,GAAG,EAAEG,KAAK,CAAC;QACjCE,UAAU,EAAGL,GAAW,IAAKX,eAAe,CAACiB,MAAM,CAACN,GAAG,CAAC;QACxDO,KAAK,EAAEA,CAAA,KAAMlB,eAAe,CAACkB,KAAK,CAAC,CAAC;QACpCC,MAAM,EAAEnB,eAAe,CAACoB,IAAI;QAC5BT,GAAG,EAAGU,KAAa,IAAKC,MAAM,CAACC,IAAI,CAACvB,eAAe,CAAC,CAACwB,EAAE,CAACH,KAAK,CAAC,IAAI;MACpE,CAAC;IACH;IAEA,MAAMI,UAAU,GACdC,MAAM,EAAE5B,YAAY,IAAIJ,MAAM,EAAEI,YAAY,IAAIA,YAAY;IAC9D,IAAI2B,UAAU,IAAI,IAAI,EAAE;MACtB,MAAM,IAAIpB,KAAK,CAAC,yCAAyC,CAAC;IAC5D;IACA,OAAOoB,UAAU;EACnB,CAAC;EAED,MAAME,WAAW,GAAG,IAAAC,oCAAiB,EAAC,CAAC;EAEvC,IAAIzB,MAAM,CAAC0B,EAAE,CAACC,QAAQ,CAAC/B,YAAY,CAAC,EAAE;IACpC,MAAM,IAAIM,KAAK,CACb,2DACF,CAAC;EACH;EAEA,MAAM0B,SAAS,GAAG,GAAG5B,MAAM,CAAC0B,EAAE,GAAG9B,YAAY,EAAE,CAAC,CAAC;EACjD,MAAMiC,WAAW,GAAIrB,GAAW,IAAK;IACnC,IAAIA,GAAG,CAACmB,QAAQ,CAAC,IAAI,CAAC,EAAE;MACtB,MAAM,IAAIzB,KAAK,CACb,4DACF,CAAC;IACH;IACA,OAAO,GAAG0B,SAAS,GAAGpB,GAAG,EAAE;EAC7B,CAAC;EAED,OAAO;IACLsB,QAAQ,EAAEA,CAAA,KAAM;MACd,MAAMV,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACd,OAAO,CAAC,CAAC,CAAC;MACnC,KAAK,MAAME,GAAG,IAAIY,IAAI,EAAE;QACtB,IAAIZ,GAAG,CAACuB,UAAU,CAACH,SAAS,CAAC,EAAE;UAC7BtB,OAAO,CAAC,CAAC,CAACO,UAAU,CAACL,GAAG,CAAC;QAC3B;MACF;IACF,CAAC;IACDM,MAAM,EAAGN,GAAG,IAAKF,OAAO,CAAC,CAAC,CAACO,UAAU,CAACgB,WAAW,CAACrB,GAAG,CAAC,CAAC;IACvDI,GAAG,EAAEA,CAACJ,GAAG,EAAEG,KAAK,KAAK;MACnBL,OAAO,CAAC,CAAC,CAACI,OAAO,CAACmB,WAAW,CAACrB,GAAG,CAAC,EAAEG,KAAK,CAACqB,QAAQ,CAAC,CAAC,CAAC;IACvD,CAAC;IACDC,SAAS,EAAGzB,GAAG,IAAKF,OAAO,CAAC,CAAC,CAACC,OAAO,CAACsB,WAAW,CAACrB,GAAG,CAAC,CAAC,IAAI0B,SAAS;IACpEC,SAAS,EAAG3B,GAAG,IAAK;MAClB,MAAMG,KAAK,GAAGL,OAAO,CAAC,CAAC,CAACC,OAAO,CAACsB,WAAW,CAACrB,GAAG,CAAC,CAAC;MACjD,IAAIG,KAAK,IAAI,IAAI,EAAE,OAAOuB,SAAS;MACnC,OAAOE,MAAM,CAACzB,KAAK,CAAC;IACtB,CAAC;IACD0B,UAAU,EAAG7B,GAAG,IAAK;MACnB,MAAMG,KAAK,GAAGL,OAAO,CAAC,CAAC,CAACC,OAAO,CAACsB,WAAW,CAACrB,GAAG,CAAC,CAAC;MACjD,IAAIG,KAAK,IAAI,IAAI,EAAE,OAAOuB,SAAS;MACnC,OAAOvB,KAAK,KAAK,MAAM;IACzB,CAAC;IACD2B,SAAS,EAAG9B,GAAG,IAAK;MAClB,MAAMG,KAAK,GAAGL,OAAO,CAAC,CAAC,CAACC,OAAO,CAACsB,WAAW,CAACrB,GAAG,CAAC,CAAC;MACjD,IAAIG,KAAK,IAAI,IAAI,EAAE,OAAOuB,SAAS;MACnC,OAAOV,WAAW,CAACe,MAAM,CAAC5B,KAAK,CAAC,CAAC6B,MAAM;IACzC,CAAC;IACDC,UAAU,EAAEA,CAAA,KAAM;MAChB,MAAMrB,IAAI,GAAGD,MAAM,CAACC,IAAI,CAACd,OAAO,CAAC,CAAC,CAAC;MACnC,OAAOc,IAAI,CACRsB,MAAM,CAAElC,GAAG,IAAKA,GAAG,CAACuB,UAAU,CAACH,SAAS,CAAC,CAAC,CAC1Ce,GAAG,CAAEnC,GAAG,IAAKA,GAAG,CAACoC,KAAK,CAAChB,SAAS,CAACZ,MAAM,CAAC,CAAC;IAC9C,CAAC;IACD6B,QAAQ,EAAGrC,GAAG,IAAKF,OAAO,CAAC,CAAC,CAACC,OAAO,CAACsB,WAAW,CAACrB,GAAG,CAAC,CAAC,IAAI,IAAI;IAC9DsC,OAAO,EAAEA,CAAA,KAAM;MACb,MAAM,IAAI5C,KAAK,CAAC,wCAAwC,CAAC;IAC3D,CAAC;IACDe,IAAI,EAAE,CAAC;IACP8B,UAAU,EAAE,KAAK;IACjBC,IAAI,EAAEA,CAAA,KAAM;MACV;IAAA;EAEJ,CAAC;AACH,CAAC;AAACC,OAAA,CAAAlD,UAAA,GAAAA,UAAA", "ignoreList": []}