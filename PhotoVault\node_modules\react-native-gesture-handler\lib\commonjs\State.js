"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.State = void 0;
// TODO use State from RNModule

const State = exports.State = {
  UNDETERMINED: 0,
  FAILED: 1,
  BEGAN: 2,
  CANCELLED: 3,
  ACTIVE: 4,
  END: 5
};

// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; it can be used as a type and as a value
//# sourceMappingURL=State.js.map