import React from "react";
import { View, StyleSheet, Dimensions } from "react-native";
import { Button, Text, Card, ActivityIndicator } from "react-native-paper";
import { PinInputProps } from "../types";

const { width } = Dimensions.get("window");

const PinInput: React.FC<PinInputProps> = ({
  pin,
  onPinChange,
  onSubmit,
  isLoading = false,
  error,
  minLength = 4,
  maxLength = 6,
}) => {
  const handleNumberPress = (number: string) => {
    if (pin.length < maxLength) {
      onPinChange(pin + number);
    }
  };

  const handleBackspace = () => {
    onPinChange(pin.slice(0, -1));
  };

  const handleClear = () => {
    onPinChange("");
  };

  const renderPinDots = () => {
    return (
      <View style={styles.pinDotsContainer}>
        {Array.from({ length: maxLength }, (_, index) => (
          <View
            key={index}
            style={[
              styles.pinDot,
              index < pin.length ? styles.pinDotFilled : styles.pinDotEmpty,
            ]}
          />
        ))}
      </View>
    );
  };

  const renderNumberPad = () => {
    const numbers = [
      ["1", "2", "3"],
      ["4", "5", "6"],
      ["7", "8", "9"],
      ["Clear", "0", "Back"],
    ];

    return (
      <View style={styles.numberPadContainer}>
        {numbers.map((row, rowIndex) => (
          <View key={rowIndex} style={styles.numberRow}>
            {row.map((item) => (
              <Button
                key={item}
                mode="outlined"
                style={styles.numberButton}
                labelStyle={styles.numberButtonText}
                onPress={() => {
                  if (item === "Back") {
                    handleBackspace();
                  } else if (item === "Clear") {
                    handleClear();
                  } else {
                    handleNumberPress(item);
                  }
                }}
                disabled={isLoading}
              >
                {item}
              </Button>
            ))}
          </View>
        ))}
      </View>
    );
  };

  return (
    <Card style={styles.container}>
      <Card.Content style={styles.content}>
        {renderPinDots()}

        {error && (
          <Text style={styles.errorText} variant="bodyMedium">
            {error}
          </Text>
        )}

        {renderNumberPad()}

        <Button
          mode="contained"
          style={styles.submitButton}
          onPress={onSubmit}
          disabled={pin.length < minLength || isLoading}
          loading={isLoading}
        >
          {isLoading ? "Processing..." : "Submit"}
        </Button>

        {isLoading && (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="large" />
          </View>
        )}
      </Card.Content>
    </Card>
  );
};

const styles = StyleSheet.create({
  container: {
    margin: 20,
    elevation: 4,
  },
  content: {
    padding: 20,
    alignItems: "center",
  },
  pinDotsContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 30,
  },
  pinDot: {
    width: 20,
    height: 20,
    borderRadius: 10,
    marginHorizontal: 10,
    borderWidth: 2,
  },
  pinDotEmpty: {
    backgroundColor: "transparent",
    borderColor: "#ccc",
  },
  pinDotFilled: {
    backgroundColor: "#6200ea",
    borderColor: "#6200ea",
  },
  numberPadContainer: {
    marginBottom: 20,
  },
  numberRow: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 10,
  },
  numberButton: {
    width: (width - 120) / 3,
    height: 60,
    marginHorizontal: 5,
    justifyContent: "center",
  },
  numberButtonText: {
    fontSize: 24,
    fontWeight: "bold",
  },
  submitButton: {
    width: "100%",
    paddingVertical: 8,
    marginTop: 20,
  },
  errorText: {
    color: "#d32f2f",
    textAlign: "center",
    marginBottom: 20,
  },
  loadingContainer: {
    position: "absolute",
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(255, 255, 255, 0.8)",
  },
});

export default PinInput;
