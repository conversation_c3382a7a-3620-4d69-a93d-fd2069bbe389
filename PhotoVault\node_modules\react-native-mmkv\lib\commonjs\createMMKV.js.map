{"version": 3, "names": ["_reactNative", "require", "_NativeMmkv", "_Types", "_NativeMmkvPlatformContext", "createMMKV", "config", "module", "getMMKVTurboModule", "Platform", "OS", "path", "appGroupDirectory", "getMMKVPlatformContextTurboModule", "getAppGroupDirectory", "e", "console", "error", "mode", "Mode", "instance", "__DEV__", "Error", "exports"], "sourceRoot": "../../src", "sources": ["createMMKV.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AACA,IAAAE,MAAA,GAAAF,OAAA;AACA,IAAAG,0BAAA,GAAAH,OAAA;AAEO,MAAMI,UAAU,GAAIC,MAAqB,IAAiB;EAC/D,MAAMC,MAAM,GAAG,IAAAC,8BAAkB,EAAC,CAAC;EAEnC,IAAIC,qBAAQ,CAACC,EAAE,KAAK,KAAK,EAAE;IACzB,IAAIJ,MAAM,CAACK,IAAI,IAAI,IAAI,EAAE;MACvB,IAAI;QACF;QACA,MAAMC,iBAAiB,GACrB,IAAAC,4DAAiC,EAAC,CAAC,CAACC,oBAAoB,CAAC,CAAC;QAC5D,IAAIF,iBAAiB,IAAI,IAAI,EAAE;UAC7B;UACAN,MAAM,CAACK,IAAI,GAAGC,iBAAiB;QACjC;MACF,CAAC,CAAC,OAAOG,CAAC,EAAE;QACV;QACAC,OAAO,CAACC,KAAK,CAACF,CAAC,CAAC;MAClB;IACF;EACF;EAEA,IAAI,OAAOT,MAAM,CAACY,IAAI,KAAK,QAAQ,EAAE;IACnC;IACA;IACA;IACAZ,MAAM,CAACY,IAAI,GAAGC,WAAI,CAACb,MAAM,CAACY,IAAI,CAAC;EACjC;EAEA,MAAME,QAAQ,GAAGb,MAAM,CAACF,UAAU,CAACC,MAAM,CAAC;EAC1C,IAAIe,OAAO,EAAE;IACX,IAAI,OAAOD,QAAQ,KAAK,QAAQ,IAAIA,QAAQ,IAAI,IAAI,EAAE;MACpD,MAAM,IAAIE,KAAK,CACb,oFACF,CAAC;IACH;EACF;EACA,OAAOF,QAAQ;AACjB,CAAC;AAACG,OAAA,CAAAlB,UAAA,GAAAA,UAAA", "ignoreList": []}