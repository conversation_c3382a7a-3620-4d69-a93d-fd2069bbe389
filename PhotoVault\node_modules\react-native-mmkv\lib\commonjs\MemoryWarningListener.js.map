{"version": 3, "names": ["_reactNative", "require", "addMemoryWarningListener", "mmkv", "global", "WeakRef", "FinalizationRegistry", "weakMmkv", "listener", "AppState", "addEventListener", "deref", "trim", "finalization", "l", "remove", "register"], "sourceRoot": "../../src", "sources": ["MemoryWarningListener.ts"], "mappings": ";;;;;;AAAA,IAAAA,YAAA,GAAAC,OAAA;AAIO,SAASC,wBAAwBA,CAACC,IAAmB,EAAQ;EAClE,IAAIC,MAAM,CAACC,OAAO,IAAI,IAAI,IAAID,MAAM,CAACE,oBAAoB,IAAI,IAAI,EAAE;IACjE;IACA,MAAMC,QAAQ,GAAG,IAAIF,OAAO,CAACF,IAAI,CAAC;IAClC,MAAMK,QAAQ,GAAGC,qBAAQ,CAACC,gBAAgB,CAAC,eAAe,EAAE,MAAM;MAChE;MACAH,QAAQ,CAACI,KAAK,CAAC,CAAC,EAAEC,IAAI,CAAC,CAAC;IAC1B,CAAC,CAAC;IACF;IACA,MAAMC,YAAY,GAAG,IAAIP,oBAAoB,CAC1CQ,CAA0B,IAAK;MAC9B;MACAA,CAAC,CAACC,MAAM,CAAC,CAAC;IACZ,CACF,CAAC;IACD;IACAF,YAAY,CAACG,QAAQ,CAACb,IAAI,EAAEK,QAAQ,CAAC;EACvC,CAAC,MAAM;IACL;IACA;IACAC,qBAAQ,CAACC,gBAAgB,CAAC,eAAe,EAAE,MAAM;MAC/CP,IAAI,CAACS,IAAI,CAAC,CAAC;IACb,CAAC,CAAC;EACJ;AACF", "ignoreList": []}