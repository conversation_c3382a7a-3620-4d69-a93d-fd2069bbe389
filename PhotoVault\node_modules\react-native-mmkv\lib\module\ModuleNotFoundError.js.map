{"version": 3, "names": ["NativeModules", "Platform", "BULLET_POINT", "messageWithSuggestions", "message", "suggestions", "join", "getFrameworkType", "ExpoConstants", "NativeUnimoduleProxy", "modulesConstants", "ExponentConstants", "appOwnership", "ModuleNotFoundError", "Error", "constructor", "cause", "global", "__turboModuleProxy", "push", "error", "framework", "OS"], "sourceRoot": "../../src", "sources": ["ModuleNotFoundError.ts"], "mappings": ";;AAAA,SAASA,aAAa,EAAEC,QAAQ,QAAQ,cAAc;AAOtD,MAAMC,YAAY,GAAG,MAAM;AAE3B,SAASC,sBAAsBA,CAC7BC,OAAe,EACfC,WAAqB,EACb;EACR,OAAOD,OAAO,GAAGF,YAAY,GAAGG,WAAW,CAACC,IAAI,CAACJ,YAAY,CAAC;AAChE;AAEA,SAASK,gBAAgBA,CAAA,EAAwC;EAC/D;EACA,MAAMC,aAAa,GACjBR,aAAa,CAACS,oBAAoB,EAAEC,gBAAgB,EAAEC,iBAAiB;EACzE,IAAIH,aAAa,IAAI,IAAI,EAAE;IACzB,IAAIA,aAAa,CAACI,YAAY,KAAK,MAAM,EAAE;MACzC;MACA,OAAO,SAAS;IAClB,CAAC,MAAM;MACL;MACA,OAAO,MAAM;IACf;EACF;EACA,OAAO,cAAc;AACvB;AAEA,OAAO,MAAMC,mBAAmB,SAASC,KAAK,CAAC;EAC7CC,WAAWA,CAACC,KAAe,EAAE;IAC3B;IACA,IAAIC,MAAM,CAACC,kBAAkB,IAAI,IAAI,EAAE;MACrC;MACA;MACA;MACA,MAAMd,OAAO,GACX,+HAA+H;MACjI,MAAMC,WAAqB,GAAG,EAAE;MAChCA,WAAW,CAACc,IAAI,CACd,mFACF,CAAC;MACDd,WAAW,CAACc,IAAI,CACd,sKACF,CAAC;MACD,MAAMC,KAAK,GAAGjB,sBAAsB,CAACC,OAAO,EAAEC,WAAW,CAAC;MAC1D,KAAK,CAACe,KAAK,EAAE;QAAEJ,KAAK,EAAEA;MAAM,CAAC,CAAC;MAC9B;IACF;IAEA,MAAMK,SAAS,GAAGd,gBAAgB,CAAC,CAAC;IACpC,IAAIc,SAAS,KAAK,SAAS,EAAE;MAC3B,KAAK,CACH,+GACF,CAAC;MACD;IACF;IAEA,MAAMjB,OAAO,GACX,kFAAkF;IACpF,MAAMC,WAAqB,GAAG,EAAE;IAChCA,WAAW,CAACc,IAAI,CACd,+FACF,CAAC;IACDd,WAAW,CAACc,IAAI,CACd,kNACF,CAAC;IACDd,WAAW,CAACc,IAAI,CACd,wGACF,CAAC;IACDd,WAAW,CAACc,IAAI,CAAC,gCAAgC,CAAC;IAClD,IAAIE,SAAS,KAAK,MAAM,EAAE;MACxBhB,WAAW,CAACc,IAAI,CAAC,oCAAoC,CAAC;IACxD;IACA,QAAQlB,QAAQ,CAACqB,EAAE;MACjB,KAAK,KAAK;MACV,KAAK,OAAO;QACVjB,WAAW,CAACc,IAAI,CACd,wDACF,CAAC;QACD;MACF,KAAK,SAAS;QACZd,WAAW,CAACc,IAAI,CAAC,6BAA6B,CAAC;QAC/C;MACF;QACE,MAAM,IAAIL,KAAK,CAAC,4BAA4Bb,QAAQ,CAACqB,EAAE,GAAG,CAAC;IAC/D;IAEA,MAAMF,KAAK,GAAGjB,sBAAsB,CAACC,OAAO,EAAEC,WAAW,CAAC;IAC1D,KAAK,CAACe,KAAK,EAAE;MAAEJ,KAAK,EAAEA;IAAM,CAAC,CAAC;EAChC;AACF", "ignoreList": []}