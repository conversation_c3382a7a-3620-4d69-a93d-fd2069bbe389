{"version": 3, "names": ["useRef", "useState", "useMemo", "useCallback", "useEffect", "MMKV", "isConfigurationEqual", "left", "right", "<PERSON><PERSON><PERSON>", "id", "path", "mode", "defaultInstance", "getDefaultInstance", "useMMKV", "configuration", "instance", "lastConfiguration", "current", "createMMKVHook", "getter", "key", "mmkv", "bump", "setBump", "value", "set", "v", "newValue", "delete", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Error", "listener", "addOnValueChangedListener", "<PERSON><PERSON><PERSON>", "b", "remove", "useMMKVString", "getString", "useMMKVNumber", "getNumber", "useMMKVBoolean", "getBoolean", "useMMKVBuffer", "<PERSON><PERSON><PERSON><PERSON>", "useMMKVObject", "json", "<PERSON><PERSON><PERSON>", "undefined", "JSON", "parse", "setValue", "Function", "<PERSON><PERSON><PERSON>", "currentValue", "stringify", "useMMKVListener", "valueChangedListener", "ref"], "sourceRoot": "../../src", "sources": ["hooks.ts"], "mappings": ";;AAAA,SAASA,MAAM,EAAEC,QAAQ,EAAEC,OAAO,EAAEC,WAAW,EAAEC,SAAS,QAAQ,OAAO;AACzE,SAASC,IAAI,QAAQ,QAAQ;AAG7B,SAASC,oBAAoBA,CAC3BC,IAAoB,EACpBC,KAAqB,EACZ;EACT,IAAID,IAAI,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI,EAAE,OAAOD,IAAI,IAAI,IAAI,IAAIC,KAAK,IAAI,IAAI;EAEvE,OACED,IAAI,CAACE,aAAa,KAAKD,KAAK,CAACC,aAAa,IAC1CF,IAAI,CAACG,EAAE,KAAKF,KAAK,CAACE,EAAE,IACpBH,IAAI,CAACI,IAAI,KAAKH,KAAK,CAACG,IAAI,IACxBJ,IAAI,CAACK,IAAI,KAAKJ,KAAK,CAACI,IAAI;AAE5B;AAEA,IAAIC,eAA4B,GAAG,IAAI;AACvC,SAASC,kBAAkBA,CAAA,EAAS;EAClC,IAAID,eAAe,IAAI,IAAI,EAAE;IAC3BA,eAAe,GAAG,IAAIR,IAAI,CAAC,CAAC;EAC9B;EACA,OAAOQ,eAAe;AACxB;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA,OAAO,SAASE,OAAOA,CAACC,aAA6B,EAAQ;EAC3D,MAAMC,QAAQ,GAAGjB,MAAM,CAAO,CAAC;EAC/B,MAAMkB,iBAAiB,GAAGlB,MAAM,CAAgB,CAAC;EAEjD,IAAIgB,aAAa,IAAI,IAAI,EAAE,OAAOF,kBAAkB,CAAC,CAAC;EAEtD,IACEG,QAAQ,CAACE,OAAO,IAAI,IAAI,IACxB,CAACb,oBAAoB,CAACY,iBAAiB,CAACC,OAAO,EAAEH,aAAa,CAAC,EAC/D;IACAE,iBAAiB,CAACC,OAAO,GAAGH,aAAa;IACzCC,QAAQ,CAACE,OAAO,GAAG,IAAId,IAAI,CAACW,aAAa,CAAC;EAC5C;EAEA,OAAOC,QAAQ,CAACE,OAAO;AACzB;AAEA,SAASC,cAAcA,CAIrBC,MAA0C,EAAE;EAC5C,OAAO,CACLC,GAAW,EACXL,QAAe,KACuC;IACtD,MAAMM,IAAI,GAAGN,QAAQ,IAAIH,kBAAkB,CAAC,CAAC;IAE7C,MAAM,CAACU,IAAI,EAAEC,OAAO,CAAC,GAAGxB,QAAQ,CAAC,CAAC,CAAC;IACnC,MAAMyB,KAAK,GAAGxB,OAAO,CAAC,MAAM;MAC1B;MACA;MACA;MACAsB,IAAI;MACJ,OAAOH,MAAM,CAACE,IAAI,EAAED,GAAG,CAAC;IAC1B,CAAC,EAAE,CAACC,IAAI,EAAED,GAAG,EAAEE,IAAI,CAAC,CAAC;;IAErB;IACA,MAAMG,GAAG,GAAGxB,WAAW,CACpByB,CAAa,IAAK;MACjB,MAAMC,QAAQ,GAAG,OAAOD,CAAC,KAAK,UAAU,GAAGA,CAAC,CAACP,MAAM,CAACE,IAAI,EAAED,GAAG,CAAC,CAAC,GAAGM,CAAC;MACnE,QAAQ,OAAOC,QAAQ;QACrB,KAAK,QAAQ;QACb,KAAK,QAAQ;QACb,KAAK,SAAS;UACZN,IAAI,CAACI,GAAG,CAACL,GAAG,EAAEO,QAAQ,CAAC;UACvB;QACF,KAAK,WAAW;UACdN,IAAI,CAACO,MAAM,CAACR,GAAG,CAAC;UAChB;QACF,KAAK,QAAQ;UACX,IAAIO,QAAQ,YAAYE,WAAW,EAAE;YACnCR,IAAI,CAACI,GAAG,CAACL,GAAG,EAAEO,QAAQ,CAAC;YACvB;UACF,CAAC,MAAM;YACL,MAAM,IAAIG,KAAK,CACb,sBAAsBH,QAAQ,qBAChC,CAAC;UACH;QACF;UACE,MAAM,IAAIG,KAAK,CAAC,cAAc,OAAOH,QAAQ,oBAAoB,CAAC;MACtE;IACF,CAAC,EACD,CAACP,GAAG,EAAEC,IAAI,CACZ,CAAC;;IAED;IACAnB,SAAS,CAAC,MAAM;MACd,MAAM6B,QAAQ,GAAGV,IAAI,CAACW,yBAAyB,CAAEC,UAAU,IAAK;QAC9D,IAAIA,UAAU,KAAKb,GAAG,EAAE;UACtBG,OAAO,CAAEW,CAAC,IAAKA,CAAC,GAAG,CAAC,CAAC;QACvB;MACF,CAAC,CAAC;MACF,OAAO,MAAMH,QAAQ,CAACI,MAAM,CAAC,CAAC;IAChC,CAAC,EAAE,CAACf,GAAG,EAAEC,IAAI,CAAC,CAAC;IAEf,OAAO,CAACG,KAAK,EAAEC,GAAG,CAAC;EACrB,CAAC;AACH;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMW,aAAa,GAAGlB,cAAc,CAAC,CAACH,QAAQ,EAAEK,GAAG,KACxDL,QAAQ,CAACsB,SAAS,CAACjB,GAAG,CACxB,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMkB,aAAa,GAAGpB,cAAc,CAAC,CAACH,QAAQ,EAAEK,GAAG,KACxDL,QAAQ,CAACwB,SAAS,CAACnB,GAAG,CACxB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMoB,cAAc,GAAGtB,cAAc,CAAC,CAACH,QAAQ,EAAEK,GAAG,KACzDL,QAAQ,CAAC0B,UAAU,CAACrB,GAAG,CACzB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,MAAMsB,aAAa,GAAGxB,cAAc,CAAC,CAACH,QAAQ,EAAEK,GAAG,KACxDL,QAAQ,CAAC4B,SAAS,CAACvB,GAAG,CACxB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASwB,aAAaA,CAC3BxB,GAAW,EACXL,QAAe,EAMf;EACA,MAAM,CAAC8B,IAAI,EAAEC,OAAO,CAAC,GAAGV,aAAa,CAAChB,GAAG,EAAEL,QAAQ,CAAC;EAEpD,MAAMS,KAAK,GAAGxB,OAAO,CAAC,MAAM;IAC1B,IAAI6C,IAAI,IAAI,IAAI,EAAE,OAAOE,SAAS;IAClC,OAAOC,IAAI,CAACC,KAAK,CAACJ,IAAI,CAAC;EACzB,CAAC,EAAE,CAACA,IAAI,CAAC,CAAC;EAEV,MAAMK,QAAQ,GAAGjD,WAAW,CACzByB,CAA6D,IAAK;IACjE,IAAIA,CAAC,YAAYyB,QAAQ,EAAE;MACzBL,OAAO,CAAEM,WAAW,IAAK;QACvB,MAAMC,YAAY,GAChBD,WAAW,IAAI,IAAI,GAAIJ,IAAI,CAACC,KAAK,CAACG,WAAW,CAAC,GAASL,SAAS;QAClE,MAAMpB,QAAQ,GAAGD,CAAC,CAAC2B,YAAY,CAAC;QAChC;QACA,OAAO1B,QAAQ,IAAI,IAAI,GAAGqB,IAAI,CAACM,SAAS,CAAC3B,QAAQ,CAAC,GAAGoB,SAAS;MAChE,CAAC,CAAC;IACJ,CAAC,MAAM;MACL;MACA,MAAMpB,QAAQ,GAAGD,CAAC,IAAI,IAAI,GAAGsB,IAAI,CAACM,SAAS,CAAC5B,CAAC,CAAC,GAAGqB,SAAS;MAC1DD,OAAO,CAACnB,QAAQ,CAAC;IACnB;EACF,CAAC,EACD,CAACmB,OAAO,CACV,CAAC;EAED,OAAO,CAACtB,KAAK,EAAE0B,QAAQ,CAAC;AAC1B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,OAAO,SAASK,eAAeA,CAC7BC,oBAA2C,EAC3CzC,QAAe,EACT;EACN,MAAM0C,GAAG,GAAG3D,MAAM,CAAC0D,oBAAoB,CAAC;EACxCC,GAAG,CAACxC,OAAO,GAAGuC,oBAAoB;EAElC,MAAMnC,IAAI,GAAGN,QAAQ,IAAIH,kBAAkB,CAAC,CAAC;EAE7CV,SAAS,CAAC,MAAM;IACd,MAAM6B,QAAQ,GAAGV,IAAI,CAACW,yBAAyB,CAAEC,UAAU,IAAK;MAC9DwB,GAAG,CAACxC,OAAO,CAACgB,UAAU,CAAC;IACzB,CAAC,CAAC;IACF,OAAO,MAAMF,QAAQ,CAACI,MAAM,CAAC,CAAC;EAChC,CAAC,EAAE,CAACd,IAAI,CAAC,CAAC;AACZ", "ignoreList": []}