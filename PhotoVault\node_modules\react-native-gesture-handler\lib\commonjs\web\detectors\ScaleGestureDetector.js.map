{"version": 3, "names": ["_constants", "require", "_interfaces", "ScaleGestureDetector", "inProgress", "constructor", "callbacks", "onScaleBegin", "onScale", "onScaleEnd", "spanSlop", "DEFAULT_TOUCH_SLOP", "minSpan", "onTouchEvent", "event", "tracker", "currentTime", "time", "action", "eventType", "numOfPointers", "trackedPointersCount", "streamComplete", "EventTypes", "UP", "ADDITIONAL_POINTER_UP", "CANCEL", "DOWN", "initialSpan", "config<PERSON><PERSON><PERSON>", "ADDITIONAL_POINTER_DOWN", "pointerUp", "ignoredPointer", "pointerId", "undefined", "div", "coordsSum", "getAbsoluteCoordsSum", "focusX", "x", "focusY", "y", "devSumX", "devSumY", "trackedPointers", "for<PERSON>ach", "value", "key", "Math", "abs", "abosoluteCoords", "devX", "devY", "spanX", "spanY", "span", "hypot", "wasInProgress", "_focusX", "_focusY", "prevSpan", "_currentSpan", "prevTime", "MOVE", "currentSpan", "calculateScaleFactor", "<PERSON><PERSON><PERSON><PERSON>", "exports", "default"], "sourceRoot": "../../../../src", "sources": ["web/detectors/ScaleGestureDetector.ts"], "mappings": ";;;;;;AAAA,IAAAA,UAAA,GAAAC,OAAA;AACA,IAAAC,WAAA,GAAAD,OAAA;AAUe,MAAME,oBAAoB,CAAiC;EAehEC,UAAU,GAAG,KAAK;EAK1BC,WAAWA,CAACC,SAA+B,EAAE;IAC3C,IAAI,CAACC,YAAY,GAAGD,SAAS,CAACC,YAAY;IAC1C,IAAI,CAACC,OAAO,GAAGF,SAAS,CAACE,OAAO;IAChC,IAAI,CAACC,UAAU,GAAGH,SAAS,CAACG,UAAU;IAEtC,IAAI,CAACC,QAAQ,GAAGC,6BAAkB,GAAG,CAAC;IACtC,IAAI,CAACC,OAAO,GAAG,CAAC;EAClB;EAEOC,YAAYA,CAACC,KAAmB,EAAEC,OAAuB,EAAW;IACzE,IAAI,CAACC,WAAW,GAAGF,KAAK,CAACG,IAAI;IAE7B,MAAMC,MAAkB,GAAGJ,KAAK,CAACK,SAAS;IAC1C,MAAMC,aAAa,GAAGL,OAAO,CAACM,oBAAoB;IAElD,MAAMC,cAAuB,GAC3BJ,MAAM,KAAKK,sBAAU,CAACC,EAAE,IACxBN,MAAM,KAAKK,sBAAU,CAACE,qBAAqB,IAC3CP,MAAM,KAAKK,sBAAU,CAACG,MAAM;IAE9B,IAAIR,MAAM,KAAKK,sBAAU,CAACI,IAAI,IAAIL,cAAc,EAAE;MAChD,IAAI,IAAI,CAAClB,UAAU,EAAE;QACnB,IAAI,CAACK,UAAU,CAAC,IAAI,CAAC;QACrB,IAAI,CAACL,UAAU,GAAG,KAAK;QACvB,IAAI,CAACwB,WAAW,GAAG,CAAC;MACtB;MAEA,IAAIN,cAAc,EAAE;QAClB,OAAO,IAAI;MACb;IACF;IAEA,MAAMO,aAAsB,GAC1BX,MAAM,KAAKK,sBAAU,CAACI,IAAI,IAC1BT,MAAM,KAAKK,sBAAU,CAACE,qBAAqB,IAC3CP,MAAM,KAAKK,sBAAU,CAACO,uBAAuB;IAE/C,MAAMC,SAAS,GAAGb,MAAM,KAAKK,sBAAU,CAACE,qBAAqB;IAE7D,MAAMO,cAAkC,GAAGD,SAAS,GAChDjB,KAAK,CAACmB,SAAS,GACfC,SAAS;;IAEb;;IAEA,MAAMC,GAAW,GAAGJ,SAAS,GAAGX,aAAa,GAAG,CAAC,GAAGA,aAAa;IAEjE,MAAMgB,SAAS,GAAGrB,OAAO,CAACsB,oBAAoB,CAAC,CAAC;IAEhD,MAAMC,MAAM,GAAGF,SAAS,CAACG,CAAC,GAAGJ,GAAG;IAChC,MAAMK,MAAM,GAAGJ,SAAS,CAACK,CAAC,GAAGN,GAAG;;IAEhC;;IAEA,IAAIO,OAAO,GAAG,CAAC;IACf,IAAIC,OAAO,GAAG,CAAC;IAEf5B,OAAO,CAAC6B,eAAe,CAACC,OAAO,CAAC,CAACC,KAAK,EAAEC,GAAG,KAAK;MAC9C,IAAIA,GAAG,KAAKf,cAAc,EAAE;QAC1B;MACF;MAEAU,OAAO,IAAIM,IAAI,CAACC,GAAG,CAACH,KAAK,CAACI,eAAe,CAACX,CAAC,GAAGD,MAAM,CAAC;MACrDK,OAAO,IAAIK,IAAI,CAACC,GAAG,CAACH,KAAK,CAACI,eAAe,CAACT,CAAC,GAAGD,MAAM,CAAC;IACvD,CAAC,CAAC;IAEF,MAAMW,IAAY,GAAGT,OAAO,GAAGP,GAAG;IAClC,MAAMiB,IAAY,GAAGT,OAAO,GAAGR,GAAG;IAElC,MAAMkB,KAAa,GAAGF,IAAI,GAAG,CAAC;IAC9B,MAAMG,KAAa,GAAGF,IAAI,GAAG,CAAC;IAE9B,MAAMG,IAAI,GAAGP,IAAI,CAACQ,KAAK,CAACH,KAAK,EAAEC,KAAK,CAAC;;IAErC;IACA,MAAMG,aAAsB,GAAG,IAAI,CAACrD,UAAU;IAC9C,IAAI,CAACsD,OAAO,GAAGpB,MAAM;IACrB,IAAI,CAACqB,OAAO,GAAGnB,MAAM;IAErB,IAAI,IAAI,CAACpC,UAAU,KAAKmD,IAAI,GAAG,IAAI,CAAC3C,OAAO,IAAIiB,aAAa,CAAC,EAAE;MAC7D,IAAI,CAACpB,UAAU,CAAC,IAAI,CAAC;MACrB,IAAI,CAACL,UAAU,GAAG,KAAK;MACvB,IAAI,CAACwB,WAAW,GAAG2B,IAAI;IACzB;IAEA,IAAI1B,aAAa,EAAE;MACjB,IAAI,CAACD,WAAW,GAAG,IAAI,CAACgC,QAAQ,GAAG,IAAI,CAACC,YAAY,GAAGN,IAAI;IAC7D;IAEA,IACE,CAAC,IAAI,CAACnD,UAAU,IAChBmD,IAAI,IAAI,IAAI,CAAC3C,OAAO,KACnB6C,aAAa,IAAIT,IAAI,CAACC,GAAG,CAACM,IAAI,GAAG,IAAI,CAAC3B,WAAW,CAAC,GAAG,IAAI,CAAClB,QAAQ,CAAC,EACpE;MACA,IAAI,CAACkD,QAAQ,GAAG,IAAI,CAACC,YAAY,GAAGN,IAAI;MACxC,IAAI,CAACO,QAAQ,GAAG,IAAI,CAAC9C,WAAW;MAChC,IAAI,CAACZ,UAAU,GAAG,IAAI,CAACG,YAAY,CAAC,IAAI,CAAC;IAC3C;;IAEA;IACA,IAAIW,MAAM,KAAKK,sBAAU,CAACwC,IAAI,EAAE;MAC9B,OAAO,IAAI;IACb;IAEA,IAAI,CAACF,YAAY,GAAGN,IAAI;IAExB,IAAI,IAAI,CAACnD,UAAU,IAAI,CAAC,IAAI,CAACI,OAAO,CAAC,IAAI,CAAC,EAAE;MAC1C,OAAO,IAAI;IACb;IAEA,IAAI,CAACoD,QAAQ,GAAG,IAAI,CAACI,WAAW;IAChC,IAAI,CAACF,QAAQ,GAAG,IAAI,CAAC9C,WAAW;IAEhC,OAAO,IAAI;EACb;EAEOiD,oBAAoBA,CAAC7C,aAAqB,EAAU;IACzD,IAAIA,aAAa,GAAG,CAAC,EAAE;MACrB,OAAO,CAAC;IACV;IAEA,OAAO,IAAI,CAACwC,QAAQ,GAAG,CAAC,GAAG,IAAI,CAACI,WAAW,GAAG,IAAI,CAACJ,QAAQ,GAAG,CAAC;EACjE;EAEA,IAAWI,WAAWA,CAAA,EAAG;IACvB,OAAO,IAAI,CAACH,YAAY;EAC1B;EAEA,IAAWvB,MAAMA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACoB,OAAO;EACrB;EAEA,IAAWlB,MAAMA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACmB,OAAO;EACrB;EAEA,IAAWO,SAASA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAClD,WAAW,GAAG,IAAI,CAAC8C,QAAQ;EACzC;AACF;AAACK,OAAA,CAAAC,OAAA,GAAAjE,oBAAA", "ignoreList": []}