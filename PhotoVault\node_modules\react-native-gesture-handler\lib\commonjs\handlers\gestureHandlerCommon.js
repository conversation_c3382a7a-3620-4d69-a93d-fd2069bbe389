"use strict";

Object.defineProperty(exports, "__esModule", {
  value: true
});
exports.baseGestureHandlerWithDetectorProps = exports.baseGestureHandlerProps = exports.MouseButton = void 0;
// Previous types exported gesture handlers as classes which creates an interface and variable, both named the same as class.
// Without those types, we'd introduce breaking change, forcing users to prefix every handler type specification with typeof
// e.g. React.createRef<TapGestureHandler> -> React.createRef<typeof TapGestureHandler>.
// See https://www.typescriptlang.org/docs/handbook/classes.html#constructor-functions for reference.

const commonProps = ['id', 'enabled', 'shouldCancelWhenOutside', 'hitSlop', 'cancelsTouchesInView', 'userSelect', 'activeCursor', 'mouseButton', 'enableContextMenu', 'touchAction'];
const componentInteractionProps = ['waitFor', 'simultaneousHandlers', 'blocksHandlers'];
const baseGestureHandlerProps = exports.baseGestureHandlerProps = [...commonProps, ...componentInteractionProps, 'onBegan', 'onFailed', 'onCancelled', 'onActivated', 'onEnded', 'onGestureEvent', 'onHandlerStateChange'];
const baseGestureHandlerWithDetectorProps = exports.baseGestureHandlerWithDetectorProps = [...commonProps, 'needsPointerData', 'manualActivation'];
let MouseButton = exports.MouseButton = /*#__PURE__*/function (MouseButton) {
  MouseButton[MouseButton["LEFT"] = 1] = "LEFT";
  MouseButton[MouseButton["RIGHT"] = 2] = "RIGHT";
  MouseButton[MouseButton["MIDDLE"] = 4] = "MIDDLE";
  MouseButton[MouseButton["BUTTON_4"] = 8] = "BUTTON_4";
  MouseButton[MouseButton["BUTTON_5"] = 16] = "BUTTON_5";
  MouseButton[MouseButton["ALL"] = 31] = "ALL";
  return MouseButton;
}({}); // TODO(TS) events in handlers
// Events payloads are types instead of interfaces due to TS limitation.
// See https://github.com/microsoft/TypeScript/issues/15300 for more info.
//# sourceMappingURL=gestureHandlerCommon.js.map