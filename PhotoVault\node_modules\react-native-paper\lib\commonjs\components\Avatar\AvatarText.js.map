{"version": 3, "names": ["React", "_interopRequireWildcard", "require", "_reactNative", "_theming", "_colors", "_getContrastingColor", "_interopRequireDefault", "_Text", "e", "__esModule", "default", "t", "WeakMap", "r", "n", "o", "i", "f", "__proto__", "has", "get", "set", "hasOwnProperty", "call", "Object", "defineProperty", "getOwnPropertyDescriptor", "_extends", "assign", "bind", "arguments", "length", "apply", "defaultSize", "AvatarText", "label", "size", "style", "labelStyle", "color", "customColor", "theme", "themeOverrides", "maxFontSizeMultiplier", "rest", "_theme$colors", "useInternalTheme", "backgroundColor", "colors", "primary", "restStyle", "StyleSheet", "flatten", "textColor", "getContrastingColor", "white", "fontScale", "useWindowDimensions", "createElement", "View", "width", "height", "borderRadius", "styles", "container", "text", "fontSize", "lineHeight", "numberOfLines", "displayName", "create", "justifyContent", "alignItems", "textAlign", "textAlignVertical", "_default", "exports"], "sourceRoot": "../../../../src", "sources": ["components/Avatar/AvatarText.tsx"], "mappings": ";;;;;;AAAA,IAAAA,KAAA,GAAAC,uBAAA,CAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AASA,IAAAE,QAAA,GAAAF,OAAA;AACA,IAAAG,OAAA,GAAAH,OAAA;AAEA,IAAAI,oBAAA,GAAAC,sBAAA,CAAAL,OAAA;AACA,IAAAM,KAAA,GAAAD,sBAAA,CAAAL,OAAA;AAAsC,SAAAK,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAAA,SAAAR,wBAAAQ,CAAA,EAAAG,CAAA,6BAAAC,OAAA,MAAAC,CAAA,OAAAD,OAAA,IAAAE,CAAA,OAAAF,OAAA,YAAAZ,uBAAA,YAAAA,CAAAQ,CAAA,EAAAG,CAAA,SAAAA,CAAA,IAAAH,CAAA,IAAAA,CAAA,CAAAC,UAAA,SAAAD,CAAA,MAAAO,CAAA,EAAAC,CAAA,EAAAC,CAAA,KAAAC,SAAA,QAAAR,OAAA,EAAAF,CAAA,iBAAAA,CAAA,uBAAAA,CAAA,yBAAAA,CAAA,SAAAS,CAAA,MAAAF,CAAA,GAAAJ,CAAA,GAAAG,CAAA,GAAAD,CAAA,QAAAE,CAAA,CAAAI,GAAA,CAAAX,CAAA,UAAAO,CAAA,CAAAK,GAAA,CAAAZ,CAAA,GAAAO,CAAA,CAAAM,GAAA,CAAAb,CAAA,EAAAS,CAAA,gBAAAN,CAAA,IAAAH,CAAA,gBAAAG,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAf,CAAA,EAAAG,CAAA,OAAAK,CAAA,IAAAD,CAAA,GAAAS,MAAA,CAAAC,cAAA,KAAAD,MAAA,CAAAE,wBAAA,CAAAlB,CAAA,EAAAG,CAAA,OAAAK,CAAA,CAAAI,GAAA,IAAAJ,CAAA,CAAAK,GAAA,IAAAN,CAAA,CAAAE,CAAA,EAAAN,CAAA,EAAAK,CAAA,IAAAC,CAAA,CAAAN,CAAA,IAAAH,CAAA,CAAAG,CAAA,WAAAM,CAAA,KAAAT,CAAA,EAAAG,CAAA;AAAA,SAAAgB,SAAA,WAAAA,QAAA,GAAAH,MAAA,CAAAI,MAAA,GAAAJ,MAAA,CAAAI,MAAA,CAAAC,IAAA,eAAAf,CAAA,aAAAN,CAAA,MAAAA,CAAA,GAAAsB,SAAA,CAAAC,MAAA,EAAAvB,CAAA,UAAAG,CAAA,GAAAmB,SAAA,CAAAtB,CAAA,YAAAK,CAAA,IAAAF,CAAA,OAAAW,cAAA,CAAAC,IAAA,CAAAZ,CAAA,EAAAE,CAAA,MAAAC,CAAA,CAAAD,CAAA,IAAAF,CAAA,CAAAE,CAAA,aAAAC,CAAA,KAAAa,QAAA,CAAAK,KAAA,OAAAF,SAAA;AAEtC,MAAMG,WAAW,GAAG,EAAE;AAiCtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,GAAGA,CAAC;EAClBC,KAAK;EACLC,IAAI,GAAGH,WAAW;EAClBI,KAAK;EACLC,UAAU;EACVC,KAAK,EAAEC,WAAW;EAClBC,KAAK,EAAEC,cAAc;EACrBC,qBAAqB;EACrB,GAAGC;AACE,CAAC,KAAK;EAAA,IAAAC,aAAA;EACX,MAAMJ,KAAK,GAAG,IAAAK,yBAAgB,EAACJ,cAAc,CAAC;EAC9C,MAAM;IAAEK,eAAe,IAAAF,aAAA,GAAGJ,KAAK,CAACO,MAAM,cAAAH,aAAA,uBAAZA,aAAA,CAAcI,OAAO;IAAE,GAAGC;EAAU,CAAC,GAC7DC,uBAAU,CAACC,OAAO,CAACf,KAAK,CAAC,IAAI,CAAC,CAAC;EACjC,MAAMgB,SAAS,GACbb,WAAW,IACX,IAAAc,4BAAmB,EAACP,eAAe,EAAEQ,aAAK,EAAE,oBAAoB,CAAC;EACnE,MAAM;IAAEC;EAAU,CAAC,GAAG,IAAAC,gCAAmB,EAAC,CAAC;EAE3C,oBACE1D,KAAA,CAAA2D,aAAA,CAACxD,YAAA,CAAAyD,IAAI,EAAAhC,QAAA;IACHU,KAAK,EAAE,CACL;MACEuB,KAAK,EAAExB,IAAI;MACXyB,MAAM,EAAEzB,IAAI;MACZ0B,YAAY,EAAE1B,IAAI,GAAG,CAAC;MACtBW;IACF,CAAC,EACDgB,MAAM,CAACC,SAAS,EAChBd,SAAS;EACT,GACEN,IAAI,gBAER7C,KAAA,CAAA2D,aAAA,CAACnD,KAAA,CAAAG,OAAI;IACH2B,KAAK,EAAE,CACL0B,MAAM,CAACE,IAAI,EACX;MACE1B,KAAK,EAAEc,SAAS;MAChBa,QAAQ,EAAE9B,IAAI,GAAG,CAAC;MAClB+B,UAAU,EAAE/B,IAAI,GAAGoB;IACrB,CAAC,EACDlB,UAAU,CACV;IACF8B,aAAa,EAAE,CAAE;IACjBzB,qBAAqB,EAAEA;EAAsB,GAE5CR,KACG,CACF,CAAC;AAEX,CAAC;AAEDD,UAAU,CAACmC,WAAW,GAAG,aAAa;AAEtC,MAAMN,MAAM,GAAGZ,uBAAU,CAACmB,MAAM,CAAC;EAC/BN,SAAS,EAAE;IACTO,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE;EACd,CAAC;EACDP,IAAI,EAAE;IACJQ,SAAS,EAAE,QAAQ;IACnBC,iBAAiB,EAAE;EACrB;AACF,CAAC,CAAC;AAAC,IAAAC,QAAA,GAAAC,OAAA,CAAAlE,OAAA,GAEYwB,UAAU", "ignoreList": []}