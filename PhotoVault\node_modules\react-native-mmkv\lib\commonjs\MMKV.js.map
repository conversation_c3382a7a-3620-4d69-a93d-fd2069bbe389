{"version": 3, "names": ["_createMMKV", "require", "_createMMKV2", "_PlatformChecker", "_MemoryWarningListener", "onValueChangedListeners", "Map", "MMKV", "constructor", "configuration", "id", "nativeInstance", "isTest", "createMockMMKV", "createMMKV", "functionCache", "addMemoryWarningListener", "has", "set", "get", "getFunctionFromCache", "functionName", "onValuesChanged", "keys", "length", "key", "listener", "size", "isReadOnly", "value", "func", "getBoolean", "getString", "getNumber", "<PERSON><PERSON><PERSON><PERSON>", "contains", "delete", "getAllKeys", "clearAll", "recrypt", "trim", "toString", "join", "toJSON", "addOnValueChangedListener", "onValueChanged", "push", "remove", "index", "indexOf", "splice", "exports"], "sourceRoot": "../../src", "sources": ["MMKV.ts"], "mappings": ";;;;;;AAAA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,YAAA,GAAAD,OAAA;AACA,IAAAE,gBAAA,GAAAF,OAAA;AAOA,IAAAG,sBAAA,GAAAH,OAAA;AAEA,MAAMI,uBAAuB,GAAG,IAAIC,GAAG,CAAoC,CAAC;;AAE5E;AACA;AACA;AACO,MAAMC,IAAI,CAA0B;EAKzC;AACF;AACA;AACA;EACEC,WAAWA,CAACC,aAA4B,GAAG;IAAEC,EAAE,EAAE;EAAe,CAAC,EAAE;IACjE,IAAI,CAACA,EAAE,GAAGD,aAAa,CAACC,EAAE;IAC1B,IAAI,CAACC,cAAc,GAAG,IAAAC,uBAAM,EAAC,CAAC,GAC1B,IAAAC,2BAAc,EAAC,CAAC,GAChB,IAAAC,sBAAU,EAACL,aAAa,CAAC;IAC7B,IAAI,CAACM,aAAa,GAAG,CAAC,CAAC;IAEvB,IAAAC,+CAAwB,EAAC,IAAI,CAAC;EAChC;EAEA,IAAYX,uBAAuBA,CAAA,EAAG;IACpC,IAAI,CAACA,uBAAuB,CAACY,GAAG,CAAC,IAAI,CAACP,EAAE,CAAC,EAAE;MACzCL,uBAAuB,CAACa,GAAG,CAAC,IAAI,CAACR,EAAE,EAAE,EAAE,CAAC;IAC1C;IACA,OAAOL,uBAAuB,CAACc,GAAG,CAAC,IAAI,CAACT,EAAE,CAAC;EAC7C;EAEQU,oBAAoBA,CAC1BC,YAAe,EACA;IACf,IAAI,IAAI,CAACN,aAAa,CAACM,YAAY,CAAC,IAAI,IAAI,EAAE;MAC5C,IAAI,CAACN,aAAa,CAACM,YAAY,CAAC,GAAG,IAAI,CAACV,cAAc,CAACU,YAAY,CAAC;IACtE;IACA,OAAO,IAAI,CAACN,aAAa,CAACM,YAAY,CAAC;EACzC;EAEQC,eAAeA,CAACC,IAAc,EAAE;IACtC,IAAI,IAAI,CAAClB,uBAAuB,CAACmB,MAAM,KAAK,CAAC,EAAE;IAE/C,KAAK,MAAMC,GAAG,IAAIF,IAAI,EAAE;MACtB,KAAK,MAAMG,QAAQ,IAAI,IAAI,CAACrB,uBAAuB,EAAE;QACnDqB,QAAQ,CAACD,GAAG,CAAC;MACf;IACF;EACF;EAEA,IAAIE,IAAIA,CAAA,EAAW;IACjB,OAAO,IAAI,CAAChB,cAAc,CAACgB,IAAI;EACjC;EACA,IAAIC,UAAUA,CAAA,EAAY;IACxB,OAAO,IAAI,CAACjB,cAAc,CAACiB,UAAU;EACvC;EACAV,GAAGA,CAACO,GAAW,EAAEI,KAA8C,EAAQ;IACrE,MAAMC,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,KAAK,CAAC;IAC7CU,IAAI,CAACL,GAAG,EAAEI,KAAK,CAAC;IAEhB,IAAI,CAACP,eAAe,CAAC,CAACG,GAAG,CAAC,CAAC;EAC7B;EACAM,UAAUA,CAACN,GAAW,EAAuB;IAC3C,MAAMK,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,YAAY,CAAC;IACpD,OAAOU,IAAI,CAACL,GAAG,CAAC;EAClB;EACAO,SAASA,CAACP,GAAW,EAAsB;IACzC,MAAMK,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,WAAW,CAAC;IACnD,OAAOU,IAAI,CAACL,GAAG,CAAC;EAClB;EACAQ,SAASA,CAACR,GAAW,EAAsB;IACzC,MAAMK,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,WAAW,CAAC;IACnD,OAAOU,IAAI,CAACL,GAAG,CAAC;EAClB;EACAS,SAASA,CAACT,GAAW,EAA+B;IAClD,MAAMK,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,WAAW,CAAC;IACnD,OAAOU,IAAI,CAACL,GAAG,CAAC;EAClB;EACAU,QAAQA,CAACV,GAAW,EAAW;IAC7B,MAAMK,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,UAAU,CAAC;IAClD,OAAOU,IAAI,CAACL,GAAG,CAAC;EAClB;EACAW,MAAMA,CAACX,GAAW,EAAQ;IACxB,MAAMK,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,QAAQ,CAAC;IAChDU,IAAI,CAACL,GAAG,CAAC;IAET,IAAI,CAACH,eAAe,CAAC,CAACG,GAAG,CAAC,CAAC;EAC7B;EACAY,UAAUA,CAAA,EAAa;IACrB,MAAMP,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,YAAY,CAAC;IACpD,OAAOU,IAAI,CAAC,CAAC;EACf;EACAQ,QAAQA,CAAA,EAAS;IACf,MAAMf,IAAI,GAAG,IAAI,CAACc,UAAU,CAAC,CAAC;IAE9B,MAAMP,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,UAAU,CAAC;IAClDU,IAAI,CAAC,CAAC;IAEN,IAAI,CAACR,eAAe,CAACC,IAAI,CAAC;EAC5B;EACAgB,OAAOA,CAACd,GAAuB,EAAQ;IACrC,MAAMK,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,SAAS,CAAC;IACjD,OAAOU,IAAI,CAACL,GAAG,CAAC;EAClB;EACAe,IAAIA,CAAA,EAAS;IACX,MAAMV,IAAI,GAAG,IAAI,CAACV,oBAAoB,CAAC,MAAM,CAAC;IAC9CU,IAAI,CAAC,CAAC;EACR;EAEAW,QAAQA,CAAA,EAAW;IACjB,OAAO,SAAS,IAAI,CAAC/B,EAAE,OAAO,IAAI,CAAC2B,UAAU,CAAC,CAAC,CAACK,IAAI,CAAC,IAAI,CAAC,GAAG;EAC/D;EACAC,MAAMA,CAAA,EAAW;IACf,OAAO;MACL,CAAC,IAAI,CAACjC,EAAE,GAAG,IAAI,CAAC2B,UAAU,CAAC;IAC7B,CAAC;EACH;EAEAO,yBAAyBA,CAACC,cAAqC,EAAY;IACzE,IAAI,CAACxC,uBAAuB,CAACyC,IAAI,CAACD,cAAc,CAAC;IAEjD,OAAO;MACLE,MAAM,EAAEA,CAAA,KAAM;QACZ,MAAMC,KAAK,GAAG,IAAI,CAAC3C,uBAAuB,CAAC4C,OAAO,CAACJ,cAAc,CAAC;QAClE,IAAIG,KAAK,KAAK,CAAC,CAAC,EAAE;UAChB,IAAI,CAAC3C,uBAAuB,CAAC6C,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;QAC/C;MACF;IACF,CAAC;EACH;AACF;AAACG,OAAA,CAAA5C,IAAA,GAAAA,IAAA", "ignoreList": []}