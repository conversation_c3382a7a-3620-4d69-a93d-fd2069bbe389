import React, { useEffect, useState } from "react";
import { NavigationContainer } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { View, StyleSheet } from "react-native";
import { ActivityIndicator } from "react-native-paper";
import StorageService from "../services/StorageService";
import PinSetupScreen from "../screens/PinSetupScreen";
import PinLoginScreen from "../screens/PinLoginScreen";
import GalleryScreen from "../screens/GalleryScreen";
import CameraScreen from "../screens/CameraScreen";
import { RootStackParamList } from "../types";

const Stack = createStackNavigator<RootStackParamList>();

const AppNavigator: React.FC = () => {
  const [isLoading, setIsLoading] = useState(true);
  const [initialRoute, setInitialRoute] =
    useState<keyof RootStackParamList>("PinSetup");

  useEffect(() => {
    const determineInitialRoute = async () => {
      try {
        const isFirstLaunch = StorageService.isFirstLaunch();
        const hasPIN = StorageService.getPIN() !== undefined;

        if (isFirstLaunch || !hasPIN) {
          setInitialRoute("PinSetup");
        } else {
          setInitialRoute("PinLogin");
        }
      } catch (error) {
        console.error("Error determining initial route:", error);
        setInitialRoute("PinSetup");
      } finally {
        setIsLoading(false);
      }
    };

    determineInitialRoute();
  }, []);

  if (isLoading) {
    return (
      <View style={styles.loadingContainer}>
        <ActivityIndicator size="large" color="#6200ea" />
      </View>
    );
  }

  return (
    <NavigationContainer>
      <Stack.Navigator
        initialRouteName={initialRoute}
        screenOptions={{
          headerShown: false,
          gestureEnabled: false, // Disable swipe gestures for security
        }}
      >
        <Stack.Screen
          name="PinSetup"
          component={PinSetupScreen}
          options={{
            gestureEnabled: false,
          }}
        />
        <Stack.Screen
          name="PinLogin"
          component={PinLoginScreen}
          options={{
            gestureEnabled: false,
          }}
        />
        <Stack.Screen
          name="Gallery"
          component={GalleryScreen}
          options={{
            gestureEnabled: false,
          }}
        />
        <Stack.Screen name="Camera" component={CameraScreen} />
      </Stack.Navigator>
    </NavigationContainer>
  );
};

const styles = StyleSheet.create({
  loadingContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "#f5f5f5",
  },
});

export default AppNavigator;
