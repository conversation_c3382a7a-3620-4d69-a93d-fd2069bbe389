{"version": 3, "names": ["_color", "_interopRequireDefault", "require", "e", "__esModule", "default", "getAndroidCheckedColor", "theme", "customColor", "isV3", "colors", "primary", "accent", "getAndroidUncheckedColor", "customUncheckedColor", "onSurfaceVariant", "dark", "color", "text", "alpha", "rgb", "string", "getAndroidRippleColor", "checkedColor", "disabled", "onSurface", "fade", "getAndroidControlColor", "checked", "uncheckedColor", "onSurfaceDisabled", "getAndroidSelectionControlColor", "rippleColor", "selectionControlColor", "exports", "getIOSCheckedColor", "getIOSRippleColor", "getSelectionControlIOSColor"], "sourceRoot": "../../../../src", "sources": ["components/Checkbox/utils.ts"], "mappings": ";;;;;;AAAA,IAAAA,MAAA,GAAAC,sBAAA,CAAAC,OAAA;AAA0B,SAAAD,uBAAAE,CAAA,WAAAA,CAAA,IAAAA,CAAA,CAAAC,UAAA,GAAAD,CAAA,KAAAE,OAAA,EAAAF,CAAA;AAI1B,MAAMG,sBAAsB,GAAGA,CAAC;EAC9BC,KAAK;EACLC;AAIF,CAAC,KAAK;EACJ,IAAIA,WAAW,EAAE;IACf,OAAOA,WAAW;EACpB;EAEA,IAAID,KAAK,CAACE,IAAI,EAAE;IACd,OAAOF,KAAK,CAACG,MAAM,CAACC,OAAO;EAC7B;EAEA,OAAOJ,KAAK,CAACG,MAAM,CAACE,MAAM;AAC5B,CAAC;AAED,MAAMC,wBAAwB,GAAGA,CAAC;EAChCN,KAAK;EACLO;AAIF,CAAC,KAAK;EACJ,IAAIA,oBAAoB,EAAE;IACxB,OAAOA,oBAAoB;EAC7B;EAEA,IAAIP,KAAK,CAACE,IAAI,EAAE;IACd,OAAOF,KAAK,CAACG,MAAM,CAACK,gBAAgB;EACtC;EAEA,IAAIR,KAAK,CAACS,IAAI,EAAE;IACd,OAAO,IAAAC,cAAK,EAACV,KAAK,CAACG,MAAM,CAACQ,IAAI,CAAC,CAACC,KAAK,CAAC,GAAG,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAC3D;EAEA,OAAO,IAAAJ,cAAK,EAACV,KAAK,CAACG,MAAM,CAACQ,IAAI,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AAC5D,CAAC;AAED,MAAMC,qBAAqB,GAAGA,CAAC;EAC7Bf,KAAK;EACLgB,YAAY;EACZC;AAKF,CAAC,KAAK;EACJ,IAAIA,QAAQ,EAAE;IACZ,IAAIjB,KAAK,CAACE,IAAI,EAAE;MACd,OAAO,IAAAQ,cAAK,EAACV,KAAK,CAACG,MAAM,CAACe,SAAS,CAAC,CAACN,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IACjE;IACA,OAAO,IAAAJ,cAAK,EAACV,KAAK,CAACG,MAAM,CAACQ,IAAI,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAC5D;EAEA,OAAO,IAAAJ,cAAK,EAACM,YAAY,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC,CAACN,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AACtD,CAAC;AAED,MAAMM,sBAAsB,GAAGA,CAAC;EAC9BpB,KAAK;EACLqB,OAAO;EACPJ,QAAQ;EACRD,YAAY;EACZM;AAOF,CAAC,KAAK;EACJ,IAAIL,QAAQ,EAAE;IACZ,IAAIjB,KAAK,CAACE,IAAI,EAAE;MACd,OAAOF,KAAK,CAACG,MAAM,CAACoB,iBAAiB;IACvC;IACA,OAAOvB,KAAK,CAACG,MAAM,CAACc,QAAQ;EAC9B;EAEA,IAAII,OAAO,EAAE;IACX,OAAOL,YAAY;EACrB;EACA,OAAOM,cAAc;AACvB,CAAC;AAEM,MAAME,+BAA+B,GAAGA,CAAC;EAC9CxB,KAAK;EACLiB,QAAQ;EACRI,OAAO;EACPpB,WAAW;EACXM;AAOF,CAAC,KAAK;EACJ,MAAMS,YAAY,GAAGjB,sBAAsB,CAAC;IAAEC,KAAK;IAAEC;EAAY,CAAC,CAAC;EACnE,MAAMqB,cAAc,GAAGhB,wBAAwB,CAAC;IAC9CN,KAAK;IACLO;EACF,CAAC,CAAC;EACF,OAAO;IACLkB,WAAW,EAAEV,qBAAqB,CAAC;MAAEf,KAAK;MAAEgB,YAAY;MAAEC;IAAS,CAAC,CAAC;IACrES,qBAAqB,EAAEN,sBAAsB,CAAC;MAC5CpB,KAAK;MACLiB,QAAQ;MACRI,OAAO;MACPL,YAAY;MACZM;IACF,CAAC;EACH,CAAC;AACH,CAAC;AAACK,OAAA,CAAAH,+BAAA,GAAAA,+BAAA;AAEF,MAAMI,kBAAkB,GAAGA,CAAC;EAC1B5B,KAAK;EACLiB,QAAQ;EACRhB;AAKF,CAAC,KAAK;EACJ,IAAIgB,QAAQ,EAAE;IACZ,IAAIjB,KAAK,CAACE,IAAI,EAAE;MACd,OAAOF,KAAK,CAACG,MAAM,CAACoB,iBAAiB;IACvC;IACA,OAAOvB,KAAK,CAACG,MAAM,CAACc,QAAQ;EAC9B;EAEA,IAAIhB,WAAW,EAAE;IACf,OAAOA,WAAW;EACpB;EAEA,IAAID,KAAK,CAACE,IAAI,EAAE;IACd,OAAOF,KAAK,CAACG,MAAM,CAACC,OAAO;EAC7B;EAEA,OAAOJ,KAAK,CAACG,MAAM,CAACE,MAAM;AAC5B,CAAC;AAED,MAAMwB,iBAAiB,GAAGA,CAAC;EACzB7B,KAAK;EACLgB,YAAY;EACZC;AAKF,CAAC,KAAK;EACJ,IAAIA,QAAQ,EAAE;IACZ,IAAIjB,KAAK,CAACE,IAAI,EAAE;MACd,OAAO,IAAAQ,cAAK,EAACV,KAAK,CAACG,MAAM,CAACe,SAAS,CAAC,CAACN,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IACjE;IACA,OAAO,IAAAJ,cAAK,EAACV,KAAK,CAACG,MAAM,CAACQ,IAAI,CAAC,CAACC,KAAK,CAAC,IAAI,CAAC,CAACC,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;EAC5D;EACA,OAAO,IAAAJ,cAAK,EAACM,YAAY,CAAC,CAACG,IAAI,CAAC,IAAI,CAAC,CAACN,GAAG,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;AACtD,CAAC;AAEM,MAAMgB,2BAA2B,GAAGA,CAAC;EAC1C9B,KAAK;EACLiB,QAAQ;EACRhB;AAKF,CAAC,KAAK;EACJ,MAAMe,YAAY,GAAGY,kBAAkB,CAAC;IAAE5B,KAAK;IAAEiB,QAAQ;IAAEhB;EAAY,CAAC,CAAC;EACzE,OAAO;IACLe,YAAY;IACZS,WAAW,EAAEI,iBAAiB,CAAC;MAC7B7B,KAAK;MACLgB,YAAY;MACZC;IACF,CAAC;EACH,CAAC;AACH,CAAC;AAACU,OAAA,CAAAG,2BAAA,GAAAA,2BAAA", "ignoreList": []}