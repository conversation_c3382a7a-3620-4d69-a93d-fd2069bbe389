{"version": 3, "names": ["goBack", "type", "navigate", "args", "name", "params", "options", "console", "warn", "payload", "merge", "pop", "Error", "navigateDeprecated", "reset", "state", "setParams", "replaceParams", "preload"], "sourceRoot": "../../src", "sources": ["CommonActions.tsx"], "mappings": ";;AA+EA,OAAO,SAASA,MAAMA,CAAA,EAAW;EAC/B,OAAO;IAAEC,IAAI,EAAE;EAAU,CAAC;AAC5B;AAmBA,OAAO,SAASC,QAAQA,CAAC,GAAGC,IAAS,EAAU;EAC7C,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IAC/B,MAAM,CAACC,IAAI,EAAEC,MAAM,EAAEC,OAAO,CAAC,GAAGH,IAAI;IAEpC,IAAI,OAAOG,OAAO,KAAK,SAAS,EAAE;MAChCC,OAAO,CAACC,IAAI,CACV,sGACF,CAAC;IACH;IAEA,OAAO;MACLP,IAAI,EAAE,UAAU;MAChBQ,OAAO,EAAE;QACPL,IAAI;QACJC,MAAM;QACNK,KAAK,EAAE,OAAOJ,OAAO,KAAK,SAAS,GAAGA,OAAO,GAAGA,OAAO,EAAEI,KAAK;QAC9DC,GAAG,EAAEL,OAAO,EAAEK;MAChB;IACF,CAAC;EACH,CAAC,MAAM;IACL,MAAMF,OAAO,GAAGN,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAE7B,IAAI,EAAE,MAAM,IAAIM,OAAO,CAAC,EAAE;MACxB,MAAM,IAAIG,KAAK,CACb,8JACF,CAAC;IACH;IAEA,OAAO;MAAEX,IAAI,EAAE,UAAU;MAAEQ;IAAQ,CAAC;EACtC;AACF;AAEA,OAAO,SAASI,kBAAkBA,CAChC,GAAGV,IAG6C,EACxC;EACR,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,QAAQ,EAAE;IAC/B,OAAO;MACLF,IAAI,EAAE,qBAAqB;MAC3BQ,OAAO,EAAE;QAAEL,IAAI,EAAED,IAAI,CAAC,CAAC,CAAC;QAAEE,MAAM,EAAEF,IAAI,CAAC,CAAC;MAAE;IAC5C,CAAC;EACH,CAAC,MAAM;IACL,MAAMM,OAAO,GAAGN,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;IAE7B,IAAI,EAAE,MAAM,IAAIM,OAAO,CAAC,EAAE;MACxB,MAAM,IAAIG,KAAK,CACb,8KACF,CAAC;IACH;IAEA,OAAO;MAAEX,IAAI,EAAE,qBAAqB;MAAEQ;IAAQ,CAAC;EACjD;AACF;AAEA,OAAO,SAASK,KAAKA,CAACC,KAA6B,EAAE;EACnD,OAAO;IAAEd,IAAI,EAAE,OAAO;IAAEQ,OAAO,EAAEM;EAAM,CAAC;AAC1C;AAEA,OAAO,SAASC,SAASA,CAACX,MAAc,EAAE;EACxC,OAAO;IACLJ,IAAI,EAAE,YAAY;IAClBQ,OAAO,EAAE;MAAEJ;IAAO;EACpB,CAAC;AACH;AAEA,OAAO,SAASY,aAAaA,CAACZ,MAAc,EAAE;EAC5C,OAAO;IACLJ,IAAI,EAAE,gBAAgB;IACtBQ,OAAO,EAAE;MAAEJ;IAAO;EACpB,CAAC;AACH;AAEA,OAAO,SAASa,OAAOA,CAACd,IAAY,EAAEC,MAAe,EAAE;EACrD,OAAO;IACLJ,IAAI,EAAE,SAAS;IACfQ,OAAO,EAAE;MAAEL,IAAI;MAAEC;IAAO;EAC1B,CAAC;AACH", "ignoreList": []}