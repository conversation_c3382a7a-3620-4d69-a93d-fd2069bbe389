{"version": 3, "sources": ["../src/createWithTheme.js"], "names": ["createWithTheme", "ThemeProvider", "ThemeContext", "withTheme", "Comp", "ThemedComponent", "a", "b", "previous", "_previous", "result", "render", "props", "_reactThemeProviderForwardedRef", "rest", "theme", "_merge", "React", "Component", "ResultComponent", "forwardRef", "ref", "displayName", "name"], "mappings": ";;;;;AAEA;;AACA;;AACA;;;;;;;;;;;;;;;;AAYA,IAAMA,eAAe,GAAG,SAAlBA,eAAkB,CACtBC,aADsB,EAEtBC,YAFsB;AAAA,SAItB,SAASC,SAAT,CAAmBC,IAAnB,EAA4B;AAAA,QACpBC,eADoB;AAAA;AAAA;AAAA;;AAAA;AAAA;;AAAA;AAAA;AAAA;;AAAA;;AAAA;;AAAA,iEAIf,UAACC,CAAD,EAAOC,CAAP,EAAiB;AACxB,cAAMC,QAAQ,GAAG,MAAKC,SAAtB;;AAEA,cAAID,QAAQ,IAAIA,QAAQ,CAACF,CAAT,KAAeA,CAA3B,IAAgCE,QAAQ,CAACD,CAAT,KAAeA,CAAnD,EAAsD;AACpD,mBAAOC,QAAQ,CAACE,MAAhB;AACD;;AAED,cAAMA,MAAM,GAAGJ,CAAC,IAAIC,CAAL,IAAUD,CAAC,KAAKC,CAAhB,GAAoB,wBAAUD,CAAV,EAAaC,CAAb,CAApB,GAAsCD,CAAC,IAAIC,CAA1D;AAEA,gBAAKE,SAAL,GAAiB;AAAEH,YAAAA,CAAC,EAADA,CAAF;AAAKC,YAAAA,CAAC,EAADA,CAAL;AAAQG,YAAAA,MAAM,EAANA;AAAR,WAAjB;AAEA,iBAAOA,MAAP;AACD,SAhBuB;;AAAA;AAAA;;AAAA;;AAAA,aAkBxBC,MAlBwB,GAkBxB,kBAAS;AAAA;;AAAA,0BAC8C,KAAKC,KADnD;AAAA,YACCC,+BADD,eACCA,+BADD;AAAA,YACqCC,IADrC;;AAGP,eACE,oBAAC,YAAD,CAAc,QAAd,QACG,UAAAC,KAAK;AAAA,iBACJ,oBAAC,IAAD,eACMD,IADN;AAEE,YAAA,KAAK,EAAE,MAAI,CAACE,MAAL,CAAYD,KAAZ,EAAmBD,IAAI,CAACC,KAAxB,CAFT;AAGE,YAAA,GAAG,EAAEF;AAHP,aADI;AAAA,SADR,CADF;AAWD,OAhCuB;;AAAA;AAAA,MACII,KAAK,CAACC,SADV;;AAmC1B,QAAMC,eAAe,GAAGF,KAAK,CAACG,UAAN,CAAiB,UAACR,KAAD,EAAQS,GAAR;AAAA,aACvC,oBAAC,eAAD,eAAqBT,KAArB;AAA4B,QAAA,+BAA+B,EAAES;AAA7D,SADuC;AAAA,KAAjB,CAAxB;AAIAF,IAAAA,eAAe,CAACG,WAAhB,mBAA2ClB,IAAI,CAACkB,WAAL,IAAoBlB,IAAI,CAACmB,IAApE;AAEA,uCAAqBJ,eAArB,EAAsCf,IAAtC;AAEA,WAAQe,eAAR;AACD,GAhDqB;AAAA,CAAxB;;eAkDenB,e", "sourcesContent": ["/* @flow */\n\nimport * as React from 'react';\nimport deepmerge from 'deepmerge';\nimport hoistNonReactStatics from 'hoist-non-react-statics';\n\nimport type { ThemeProviderType } from './createThemeProvider';\nimport type { $DeepShape } from './types';\n\nexport type WithThemeType<T> = <P, C: React.ComponentType<P>>(\n  Comp: C\n) => C &\n  React.ComponentType<\n    $Diff<React.ElementConfig<C>, { theme: T }> & { theme?: $DeepShape<T> }\n  >;\n\nconst createWithTheme = <T: Object, S: $DeepShape<T>>(\n  ThemeProvider: ThemeProviderType<T>,\n  ThemeContext: React.Context<T>\n) =>\n  function withTheme(Comp: *) {\n    class ThemedComponent extends React.Component<*> {\n      _previous: ?{ a: T, b: ?S, result: T };\n\n      _merge = (a: T, b: ?S) => {\n        const previous = this._previous;\n\n        if (previous && previous.a === a && previous.b === b) {\n          return previous.result;\n        }\n\n        const result = a && b && a !== b ? deepmerge(a, b) : a || b;\n\n        this._previous = { a, b, result };\n\n        return result;\n      };\n\n      render() {\n        const { _reactThemeProviderForwardedRef, ...rest } = this.props;\n\n        return (\n          <ThemeContext.Consumer>\n            {theme => (\n              <Comp\n                {...rest}\n                theme={this._merge(theme, rest.theme)}\n                ref={_reactThemeProviderForwardedRef}\n              />\n            )}\n          </ThemeContext.Consumer>\n        );\n      }\n    }\n\n    const ResultComponent = React.forwardRef((props, ref) => (\n      <ThemedComponent {...props} _reactThemeProviderForwardedRef={ref} />\n    ));\n\n    ResultComponent.displayName = `withTheme(${Comp.displayName || Comp.name})`;\n\n    hoistNonReactStatics(ResultComponent, Comp);\n\n    return (ResultComponent: any);\n  };\n\nexport default createWithTheme;\n"], "file": "createWithTheme.js"}