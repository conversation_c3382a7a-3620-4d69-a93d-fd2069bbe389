{"version": 3, "names": ["_Directions", "require", "_constants", "Vector", "constructor", "x", "y", "_magnitude", "Math", "hypot", "isMagnitudeSufficient", "MINIMAL_RECOGNIZABLE_MAGNITUDE", "unitX", "unitY", "fromDirection", "direction", "DirectionToVectorMappings", "get", "fromVelocity", "tracker", "pointerId", "velocity", "getVelocity", "magnitude", "computeSimilarity", "vector", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "threshold", "exports", "default", "Map", "Directions", "LEFT", "RIGHT", "UP", "DOWN", "DiagonalDirections", "UP_RIGHT", "DOWN_RIGHT", "UP_LEFT", "DOWN_LEFT"], "sourceRoot": "../../../../src", "sources": ["web/tools/Vector.ts"], "mappings": ";;;;;;AAAA,IAAAA,WAAA,GAAAC,OAAA;AACA,IAAAC,UAAA,GAAAD,OAAA;AAGe,MAAME,MAAM,CAAC;EAO1BC,WAAWA,CAACC,CAAS,EAAEC,CAAS,EAAE;IAChC,IAAI,CAACD,CAAC,GAAGA,CAAC;IACV,IAAI,CAACC,CAAC,GAAGA,CAAC;IAEV,IAAI,CAACC,UAAU,GAAGC,IAAI,CAACC,KAAK,CAAC,IAAI,CAACJ,CAAC,EAAE,IAAI,CAACC,CAAC,CAAC;IAC5C,MAAMI,qBAAqB,GACzB,IAAI,CAACH,UAAU,GAAGI,yCAA8B;IAElD,IAAI,CAACC,KAAK,GAAGF,qBAAqB,GAAG,IAAI,CAACL,CAAC,GAAG,IAAI,CAACE,UAAU,GAAG,CAAC;IACjE,IAAI,CAACM,KAAK,GAAGH,qBAAqB,GAAG,IAAI,CAACJ,CAAC,GAAG,IAAI,CAACC,UAAU,GAAG,CAAC;EACnE;EAEA,OAAOO,aAAaA,CAACC,SAA0C,EAAU;IACvE,OAAOC,yBAAyB,CAACC,GAAG,CAACF,SAAS,CAAC,IAAI,IAAIZ,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EACrE;EAEA,OAAOe,YAAYA,CAACC,OAAuB,EAAEC,SAAiB,EAAE;IAC9D,MAAMC,QAAQ,GAAGF,OAAO,CAACG,WAAW,CAACF,SAAS,CAAC;IAC/C,OAAOC,QAAQ,GAAG,IAAIlB,MAAM,CAACkB,QAAQ,CAAChB,CAAC,EAAEgB,QAAQ,CAACf,CAAC,CAAC,GAAG,IAAI;EAC7D;EAEA,IAAWiB,SAASA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAChB,UAAU;EACxB;EAEAiB,iBAAiBA,CAACC,MAAc,EAAE;IAChC,OAAO,IAAI,CAACb,KAAK,GAAGa,MAAM,CAACb,KAAK,GAAG,IAAI,CAACC,KAAK,GAAGY,MAAM,CAACZ,KAAK;EAC9D;EAEAa,SAASA,CAACD,MAAc,EAAEE,SAAiB,EAAE;IAC3C,OAAO,IAAI,CAACH,iBAAiB,CAACC,MAAM,CAAC,GAAGE,SAAS;EACnD;AACF;AAACC,OAAA,CAAAC,OAAA,GAAA1B,MAAA;AAED,MAAMa,yBAAyB,GAAG,IAAIc,GAAG,CAGvC,CACA,CAACC,sBAAU,CAACC,IAAI,EAAE,IAAI7B,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACpC,CAAC4B,sBAAU,CAACE,KAAK,EAAE,IAAI9B,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACpC,CAAC4B,sBAAU,CAACG,EAAE,EAAE,IAAI/B,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAClC,CAAC4B,sBAAU,CAACI,IAAI,EAAE,IAAIhC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAEnC,CAACiC,8BAAkB,CAACC,QAAQ,EAAE,IAAIlC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAChD,CAACiC,8BAAkB,CAACE,UAAU,EAAE,IAAInC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EACjD,CAACiC,8BAAkB,CAACG,OAAO,EAAE,IAAIpC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC,EAChD,CAACiC,8BAAkB,CAACI,SAAS,EAAE,IAAIrC,MAAM,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,CAClD,CAAC", "ignoreList": []}