import { PressableEvent } from './PressableProps';
import { PressableStateMachine } from './StateMachine';
export declare enum StateMachineEvent {
    NATIVE_BEGIN = "nativeBegin",
    NATIVE_START = "nativeStart",
    FINALIZE = "finalize",
    LONG_PRESS_TOUCHES_DOWN = "longPressTouchesDown"
}
export declare function getConfiguredStateMachine(handlePressIn: (event: PressableEvent) => void, handlePressOut: (event: PressableEvent) => void): PressableStateMachine;
