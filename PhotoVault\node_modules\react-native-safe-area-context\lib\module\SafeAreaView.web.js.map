{"version": 3, "names": ["React", "StyleSheet", "View", "useSafeAreaInsets", "defaultEdges", "top", "left", "bottom", "right", "getEdgeValue", "inset", "current", "mode", "Math", "max", "SafeAreaView", "forwardRef", "style", "edges", "rest", "ref", "insets", "edgesRecord", "useMemo", "Array", "isArray", "reduce", "acc", "edge", "appliedStyle", "flatStyle", "flatten", "margin", "marginVertical", "marginHorizontal", "marginTop", "marginRight", "marginBottom", "marginLeft", "marginStyle", "padding", "paddingVertical", "paddingHorizontal", "paddingTop", "paddingRight", "paddingBottom", "paddingLeft", "paddingStyle", "createElement", "_extends"], "sourceRoot": "../../src", "sources": ["SafeAreaView.web.tsx"], "mappings": ";AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,IAAI,QAAQ,cAAc;AAQ/C,SAASC,iBAAiB,QAAQ,mBAAmB;AAErD,MAAMC,YAAoC,GAAG;EAC3CC,GAAG,EAAE,UAAU;EACfC,IAAI,EAAE,UAAU;EAChBC,MAAM,EAAE,UAAU;EAClBC,KAAK,EAAE;AACT,CAAC;AAED,SAASC,YAAYA,CACnBC,KAAa,EACbC,OAAe,EACfC,IAA0B,EAC1B;EACA,QAAQA,IAAI;IACV,KAAK,KAAK;MACR,OAAOD,OAAO;IAChB,KAAK,SAAS;MACZ,OAAOE,IAAI,CAACC,GAAG,CAACH,OAAO,EAAED,KAAK,CAAC;IACjC,KAAK,UAAU;IACf;MACE,OAAOC,OAAO,GAAGD,KAAK;EAC1B;AACF;AAEA,OAAO,MAAMK,YAAY,gBAAGf,KAAK,CAACgB,UAAU,CAG1C,CAAC;EAAEC,KAAK,GAAG,CAAC,CAAC;EAAEL,IAAI;EAAEM,KAAK;EAAE,GAAGC;AAAK,CAAC,EAAEC,GAAG,KAAK;EAC/C,MAAMC,MAAM,GAAGlB,iBAAiB,CAAC,CAAC;EAElC,MAAMmB,WAAW,GAAGtB,KAAK,CAACuB,OAAO,CAAC,MAAM;IACtC,IAAIL,KAAK,IAAI,IAAI,EAAE;MACjB,OAAOd,YAAY;IACrB;IAEA,OAAOoB,KAAK,CAACC,OAAO,CAACP,KAAK,CAAC,GACvBA,KAAK,CAACQ,MAAM,CAAa,CAACC,GAAG,EAAEC,IAAU,KAAK;MAC5CD,GAAG,CAACC,IAAI,CAAC,GAAG,UAAU;MACtB,OAAOD,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAC;IACN;IACCT,KAAoB;EAC3B,CAAC,EAAE,CAACA,KAAK,CAAC,CAAC;EAEX,MAAMW,YAAY,GAAG7B,KAAK,CAACuB,OAAO,CAAC,MAAM;IACvC,MAAMO,SAAS,GAAG7B,UAAU,CAAC8B,OAAO,CAACd,KAAK,CAA2B;IAErE,IAAIL,IAAI,KAAK,QAAQ,EAAE;MACrB,MAAM;QACJoB,MAAM,GAAG,CAAC;QACVC,cAAc,GAAGD,MAAM;QACvBE,gBAAgB,GAAGF,MAAM;QACzBG,SAAS,GAAGF,cAAc;QAC1BG,WAAW,GAAGF,gBAAgB;QAC9BG,YAAY,GAAGJ,cAAc;QAC7BK,UAAU,GAAGJ;MACf,CAAC,GAAGJ,SAAS;MAEb,MAAMS,WAAW,GAAG;QAClBJ,SAAS,EAAE1B,YAAY,CAACY,MAAM,CAAChB,GAAG,EAAE8B,SAAS,EAAEb,WAAW,CAACjB,GAAG,CAAC;QAC/D+B,WAAW,EAAE3B,YAAY,CAACY,MAAM,CAACb,KAAK,EAAE4B,WAAW,EAAEd,WAAW,CAACd,KAAK,CAAC;QACvE6B,YAAY,EAAE5B,YAAY,CACxBY,MAAM,CAACd,MAAM,EACb8B,YAAY,EACZf,WAAW,CAACf,MACd,CAAC;QACD+B,UAAU,EAAE7B,YAAY,CAACY,MAAM,CAACf,IAAI,EAAEgC,UAAU,EAAEhB,WAAW,CAAChB,IAAI;MACpE,CAAC;MAED,OAAO,CAACW,KAAK,EAAEsB,WAAW,CAAC;IAC7B,CAAC,MAAM;MACL,MAAM;QACJC,OAAO,GAAG,CAAC;QACXC,eAAe,GAAGD,OAAO;QACzBE,iBAAiB,GAAGF,OAAO;QAC3BG,UAAU,GAAGF,eAAe;QAC5BG,YAAY,GAAGF,iBAAiB;QAChCG,aAAa,GAAGJ,eAAe;QAC/BK,WAAW,GAAGJ;MAChB,CAAC,GAAGZ,SAAS;MAEb,MAAMiB,YAAY,GAAG;QACnBJ,UAAU,EAAElC,YAAY,CAACY,MAAM,CAAChB,GAAG,EAAEsC,UAAU,EAAErB,WAAW,CAACjB,GAAG,CAAC;QACjEuC,YAAY,EAAEnC,YAAY,CACxBY,MAAM,CAACb,KAAK,EACZoC,YAAY,EACZtB,WAAW,CAACd,KACd,CAAC;QACDqC,aAAa,EAAEpC,YAAY,CACzBY,MAAM,CAACd,MAAM,EACbsC,aAAa,EACbvB,WAAW,CAACf,MACd,CAAC;QACDuC,WAAW,EAAErC,YAAY,CAACY,MAAM,CAACf,IAAI,EAAEwC,WAAW,EAAExB,WAAW,CAAChB,IAAI;MACtE,CAAC;MAED,OAAO,CAACW,KAAK,EAAE8B,YAAY,CAAC;IAC9B;EACF,CAAC,EAAE,CACDzB,WAAW,CAACf,MAAM,EAClBe,WAAW,CAAChB,IAAI,EAChBgB,WAAW,CAACd,KAAK,EACjBc,WAAW,CAACjB,GAAG,EACfgB,MAAM,CAACd,MAAM,EACbc,MAAM,CAACf,IAAI,EACXe,MAAM,CAACb,KAAK,EACZa,MAAM,CAAChB,GAAG,EACVO,IAAI,EACJK,KAAK,CACN,CAAC;EAEF,oBAAOjB,KAAA,CAAAgD,aAAA,CAAC9C,IAAI,EAAA+C,QAAA;IAAChC,KAAK,EAAEY;EAAa,GAAKV,IAAI;IAAEC,GAAG,EAAEA;EAAI,EAAE,CAAC;AAC1D,CAAC,CAAC", "ignoreList": []}