import * as ImagePicker from "expo-image-picker";
import { Alert } from "react-native";
import StorageService from "./StorageService";

class MediaService {
  async requestPermissions(): Promise<boolean> {
    try {
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();
      return status === "granted";
    } catch (error) {
      console.error("Error requesting media library permissions:", error);
      return false;
    }
  }

  async selectAndImportImage(): Promise<boolean> {
    try {
      // Check permissions first
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        Alert.alert(
          "Permission Required",
          "PhotoVault needs access to your photo library to import images.",
          [{ text: "OK" }]
        );
        return false;
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 0.8,
        allowsMultipleSelection: false,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        return false;
      }

      const selectedAsset = result.assets[0];

      // Import the selected image
      const savedImage = await StorageService.addImage(
        selectedAsset.uri,
        `imported_${Date.now()}.jpg`
      );

      if (savedImage) {
        Alert.alert(
          "Photo Imported",
          "The selected photo has been securely imported to your vault.",
          [{ text: "OK" }]
        );
        return true;
      } else {
        Alert.alert("Error", "Failed to import the selected photo.");
        return false;
      }
    } catch (error) {
      console.error("Error selecting image:", error);
      Alert.alert("Error", "Failed to access photo library.");
      return false;
    }
  }
}

export default new MediaService();
