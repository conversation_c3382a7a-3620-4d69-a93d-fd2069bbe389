import * as MediaLibrary from 'expo-media-library';
import { Alert } from 'react-native';
import StorageService from './StorageService';

class MediaService {
  async requestPermissions(): Promise<boolean> {
    try {
      const { status } = await MediaLibrary.requestPermissionsAsync();
      return status === 'granted';
    } catch (error) {
      console.error('Error requesting media library permissions:', error);
      return false;
    }
  }

  async selectAndImportImage(): Promise<boolean> {
    try {
      // Check permissions first
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        Alert.alert(
          'Permission Required',
          'PhotoVault needs access to your photo library to import images.',
          [{ text: 'OK' }]
        );
        return false;
      }

      // Get the first album (usually Camera Roll)
      const albums = await MediaLibrary.getAlbumsAsync({
        includeSmartAlbums: true,
      });

      if (albums.length === 0) {
        Alert.alert('No Photos', 'No photo albums found on your device.');
        return false;
      }

      // Get assets from the first album
      const assets = await MediaLibrary.getAssetsAsync({
        first: 20,
        mediaType: 'photo',
        sortBy: 'creationTime',
      });

      if (assets.assets.length === 0) {
        Alert.alert('No Photos', 'No photos found in your library.');
        return false;
      }

      // For simplicity, we'll show a selection of the most recent photos
      // In a real app, you might want to implement a proper photo picker
      return await this.showPhotoSelection(assets.assets);
    } catch (error) {
      console.error('Error selecting image:', error);
      Alert.alert('Error', 'Failed to access photo library.');
      return false;
    }
  }

  private async showPhotoSelection(assets: MediaLibrary.Asset[]): Promise<boolean> {
    return new Promise((resolve) => {
      // Create a simple selection dialog
      const photoOptions = assets.slice(0, 5).map((asset, index) => ({
        text: `Photo ${index + 1} (${new Date(asset.creationTime).toLocaleDateString()})`,
        onPress: async () => {
          const success = await this.importAsset(asset);
          resolve(success);
        },
      }));

      photoOptions.push({
        text: 'Cancel',
        onPress: () => resolve(false),
      });

      Alert.alert(
        'Select Photo',
        'Choose a photo to import to your vault:',
        photoOptions
      );
    });
  }

  private async importAsset(asset: MediaLibrary.Asset): Promise<boolean> {
    try {
      // Get asset info to get the local URI
      const assetInfo = await MediaLibrary.getAssetInfoAsync(asset);
      
      if (!assetInfo.localUri) {
        Alert.alert('Error', 'Could not access the selected photo.');
        return false;
      }

      // Import the image to the vault
      const savedImage = await StorageService.addImage(
        assetInfo.localUri,
        `imported_${asset.filename}`
      );

      if (savedImage) {
        Alert.alert(
          'Photo Imported',
          'The selected photo has been securely imported to your vault.',
          [{ text: 'OK' }]
        );
        return true;
      } else {
        Alert.alert('Error', 'Failed to import the selected photo.');
        return false;
      }
    } catch (error) {
      console.error('Error importing asset:', error);
      Alert.alert('Error', 'Failed to import the selected photo.');
      return false;
    }
  }

  async getRecentPhotos(limit: number = 10): Promise<MediaLibrary.Asset[]> {
    try {
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        return [];
      }

      const assets = await MediaLibrary.getAssetsAsync({
        first: limit,
        mediaType: 'photo',
        sortBy: 'creationTime',
      });

      return assets.assets;
    } catch (error) {
      console.error('Error getting recent photos:', error);
      return [];
    }
  }
}

export default new MediaService();
