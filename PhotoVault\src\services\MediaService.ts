import * as ImagePicker from "expo-image-picker";
import * as MediaLibrary from "expo-media-library";
import { Alert } from "react-native";
import StorageService from "./StorageService";

class MediaService {
  async requestPermissions(): Promise<boolean> {
    try {
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();
      return status === "granted";
    } catch (error) {
      console.error("Error requesting media library permissions:", error);
      return false;
    }
  }

  async selectAndImportImage(): Promise<boolean> {
    return new Promise((resolve) => {
      Alert.alert(
        "🔐 Secure Import",
        "⚠️ IMPORTANT: Due to platform limitations, photos cannot be automatically deleted from your gallery.\n\nAfter importing, you should manually delete the original from your gallery for maximum security.\n\nProceed with import?",
        [
          {
            text: "Cancel",
            style: "cancel",
            onPress: () => resolve(false),
          },
          {
            text: "Import & Remind Me",
            style: "default",
            onPress: async () => {
              const result = await this.importImage(true);
              resolve(result);
            },
          },
        ]
      );
    });
  }

  private async importImage(secureImport: boolean): Promise<boolean> {
    try {
      // Check permissions first
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        Alert.alert(
          "Permission Required",
          "LocalOne Vault needs access to your photo library to import images.",
          [{ text: "OK" }]
        );
        return false;
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 0.8,
        allowsMultipleSelection: false,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        return false;
      }

      const selectedAsset = result.assets[0];

      // Import the selected image
      const savedImage = await StorageService.addImage(
        selectedAsset.uri,
        `imported_${Date.now()}.jpg`
      );

      if (savedImage) {
        if (secureImport) {
          // Try automatic deletion in development builds
          const deletionSuccess = await this.attemptAutomaticDeletion(
            selectedAsset
          );

          if (deletionSuccess) {
            Alert.alert(
              "✅ Secure Import Complete",
              "🔐 SUCCESS: Your photo has been securely moved to LocalOne Vault and automatically removed from your gallery.\n\nThe photo now only exists in your secure vault.",
              [{ text: "Perfect!" }]
            );
          } else {
            // Fallback to manual deletion guidance
            Alert.alert(
              "✅ Photo Imported Successfully",
              "🔐 SECURITY REMINDER:\n\nYour photo has been securely saved to LocalOne Vault.\n\n⚠️ For maximum security, please manually delete the original photo from your device gallery now.\n\nThis ensures the photo only exists in your secure vault.",
              [
                {
                  text: "I'll Delete It Later",
                  style: "cancel",
                },
                {
                  text: "Open Gallery App",
                  style: "default",
                  onPress: () => {
                    Alert.alert(
                      "Manual Deletion Guide",
                      "1. Open your device's Gallery/Photos app\n2. Find the photo you just imported\n3. Delete it permanently\n4. Check 'Recently Deleted' folder and delete from there too\n\nThis ensures maximum security for your vault.",
                      [{ text: "Got It" }]
                    );
                  },
                },
              ]
            );
          }
        } else {
          Alert.alert(
            "Photo Imported",
            "The selected photo has been copied to your vault. The original remains in your gallery.",
            [{ text: "OK" }]
          );
        }
        return true;
      } else {
        Alert.alert("Error", "Failed to import the selected photo.");
        return false;
      }
    } catch (error) {
      console.error("Error selecting image:", error);
      Alert.alert("Error", "Failed to access photo library.");
      return false;
    }
  }

  private async attemptAutomaticDeletion(
    asset: ImagePicker.ImagePickerAsset
  ): Promise<boolean> {
    try {
      // Check if we're in a development build (not Expo Go)
      const isExpoGo = __DEV__ && !process.env.EXPO_PUBLIC_DEVELOPMENT_BUILD;

      if (isExpoGo) {
        console.warn("Automatic deletion not available in Expo Go");
        return false;
      }

      // In development builds, we can use MediaLibrary to delete
      // Note: This requires the asset to have an ID from MediaLibrary
      if (asset.assetId) {
        await MediaLibrary.deleteAssetsAsync([asset.assetId]);
        console.log("Successfully deleted original photo from gallery");
        return true;
      } else {
        console.warn("Asset ID not available for deletion");
        return false;
      }
    } catch (error) {
      console.error("Error attempting automatic deletion:", error);
      return false;
    }
  }
}

export default new MediaService();
