import * as ImagePicker from "expo-image-picker";
import * as MediaLibrary from "expo-media-library";
import { Alert } from "react-native";
import StorageService from "./StorageService";

class MediaService {
  async requestPermissions(): Promise<boolean> {
    try {
      const { status } =
        await ImagePicker.requestMediaLibraryPermissionsAsync();
      return status === "granted";
    } catch (error) {
      console.error("Error requesting media library permissions:", error);
      return false;
    }
  }

  async selectAndImportImage(): Promise<boolean> {
    return new Promise((resolve) => {
      Alert.alert(
        "🔐 Secure Import",
        "Choose how to import your photo:\n\n• SECURE: Photo will be moved to your vault and deleted from gallery\n• COPY: Photo will be copied to your vault (original remains in gallery)",
        [
          {
            text: "Cancel",
            style: "cancel",
            onPress: () => resolve(false),
          },
          {
            text: "Copy Only",
            style: "default",
            onPress: async () => {
              const result = await this.importImage(false);
              resolve(result);
            },
          },
          {
            text: "Secure Import",
            style: "default",
            onPress: async () => {
              const result = await this.importImage(true);
              resolve(result);
            },
          },
        ]
      );
    });
  }

  private async importImage(secureImport: boolean): Promise<boolean> {
    try {
      // Check permissions first
      const hasPermission = await this.requestPermissions();
      if (!hasPermission) {
        Alert.alert(
          "Permission Required",
          "LocalOne Vault needs access to your photo library to import images.",
          [{ text: "OK" }]
        );
        return false;
      }

      // Launch image picker
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ImagePicker.MediaTypeOptions.Images,
        allowsEditing: false,
        quality: 0.8,
        allowsMultipleSelection: false,
      });

      if (result.canceled || !result.assets || result.assets.length === 0) {
        return false;
      }

      const selectedAsset = result.assets[0];

      // Import the selected image
      const savedImage = await StorageService.addImage(
        selectedAsset.uri,
        `imported_${Date.now()}.jpg`
      );

      if (savedImage) {
        let success = true;

        // If secure import, try to delete the original from gallery
        if (secureImport) {
          success = await this.deleteOriginalFromGallery(selectedAsset);
        }

        const importType = secureImport ? "securely imported" : "copied";
        const securityNote = secureImport
          ? success
            ? " The original has been removed from your gallery."
            : " Note: Could not remove original from gallery."
          : " The original remains in your gallery.";

        Alert.alert(
          "Photo Imported",
          `The selected photo has been ${importType} to your vault.${securityNote}`,
          [{ text: "OK" }]
        );
        return true;
      } else {
        Alert.alert("Error", "Failed to import the selected photo.");
        return false;
      }
    } catch (error) {
      console.error("Error selecting image:", error);
      Alert.alert("Error", "Failed to access photo library.");
      return false;
    }
  }

  private async deleteOriginalFromGallery(
    asset: ImagePicker.ImagePickerAsset
  ): Promise<boolean> {
    try {
      // Note: In Expo Go, deleting from media library has limitations
      // This will work better in a development build or production app

      // For now, we'll show a warning that this feature requires a development build
      console.warn(
        "Secure deletion requires a development build for full functionality"
      );

      // In a real development build, you would use:
      // await MediaLibrary.deleteAssetsAsync([asset.assetId]);

      return false; // Return false to indicate original wasn't deleted
    } catch (error) {
      console.error("Error deleting original from gallery:", error);
      return false;
    }
  }
}

export default new MediaService();
